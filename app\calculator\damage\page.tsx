"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Zap, Target, Shield, Swords, Calculator, RotateCcw, Copy, Share2, AlertTriangle } from "lucide-react"

interface DamageResults {
  meleeBaseDamage: number
  meleeFinalDamage: number
  swordBaseDamage: number
  swordFinalDamage: number
  gunBaseDamage: number
  gunFinalDamage: number
  fruitBaseDamage: number
  fruitFinalDamage: number
  criticalChance: number
  criticalMultiplier: number
}

export default function DamageCalculatorPage() {
  const [attacker, setAttacker] = useState({
    level: 2550,
    meleeStats: 2550,
    swordStats: 2550,
    gunStats: 2550,
    fruitStats: 2550,
    defenseStats: 2550,
    fruit: "",
    weapon: "",
    race: "",
    haki: false,
    enhancement: 0, // 0-20 pour l'enhancement des armes
  })

  const [defender, setDefender] = useState({
    level: 2550,
    defenseStats: 2550,
    race: "",
    type: "player", // player or npc
    npcType: "normal", // normal, boss, raid_boss
  })

  const [results, setResults] = useState<DamageResults>({
    meleeBaseDamage: 0,
    meleeFinalDamage: 0,
    swordBaseDamage: 0,
    swordFinalDamage: 0,
    gunBaseDamage: 0,
    gunFinalDamage: 0,
    fruitBaseDamage: 0,
    fruitFinalDamage: 0,
    criticalChance: 0,
    criticalMultiplier: 1.5,
  })

  const [showAdvanced, setShowAdvanced] = useState(false)
  const [autoCalculate, setAutoCalculate] = useState(true)

  const fruits = [
    "None",
    "Dragon-Dragon",
    "Leopard-Leopard",
    "Spirit-Spirit",
    "Control-Control",
    "Venom-Venom",
    "Dough-Dough",
    "Shadow-Shadow",
    "Buddha-Buddha",
    "Phoenix-Phoenix",
    "Blizzard-Blizzard",
    "Rumble-Rumble",
    "Paw-Paw",
    "Gravity-Gravity",
    "Mammoth-Mammoth",
    "T-Rex-T-Rex",
    "Portal-Portal",
    "Pain-Pain",
    "Kitsune-Kitsune",
  ]

  const weapons = [
    "None",
    "Cursed Dual Katana",
    "Dark Blade",
    "True Triple Katana",
    "Yama",
    "Tushita",
    "Enma",
    "Shisui",
    "Wando",
    "Canvander",
    "Buddy Sword",
    "Saber",
    "Bisento",
    "Pole (1st Form)",
    "Rengoku",
    "Soul Cane",
  ]

  const races = ["Human", "Angel", "Shark", "Cyborg", "Ghoul", "Mink"]

  const npcTypes = [
    { value: "normal", label: "Normal NPC", defenseMultiplier: 1.0 },
    { value: "boss", label: "Boss", defenseMultiplier: 1.5 },
    { value: "raid_boss", label: "Raid Boss", defenseMultiplier: 2.0 },
    { value: "sea_beast", label: "Sea Beast", defenseMultiplier: 1.8 },
  ]

  // Validation des inputs
  const validateLevel = (value: number) => Math.min(2550, Math.max(1, value))
  const validateStats = (value: number) => Math.min(2550, Math.max(0, value))

  const handleAttackerChange = (field: string, value: any) => {
    setAttacker((prev) => {
      const newAttacker = { ...prev } as any

      if (field === "level") {
        newAttacker.level = validateLevel(Number(value) || 1)
      } else if (["meleeStats", "swordStats", "gunStats", "fruitStats", "defenseStats"].includes(field)) {
        newAttacker[field] = validateStats(Number(value) || 0)
      } else if (field === "enhancement") {
        newAttacker.enhancement = Math.min(20, Math.max(0, Number(value) || 0))
      } else {
        newAttacker[field] = value
      }

      return newAttacker
    })
  }

  const handleDefenderChange = (field: string, value: any) => {
    setDefender((prev) => {
      const newDefender = { ...prev } as any

      if (field === "level") {
        newDefender.level = validateLevel(Number(value) || 1)
      } else if (field === "defenseStats") {
        newDefender.defenseStats = validateStats(Number(value) || 0)
      } else {
        newDefender[field] = value
      }

      return newDefender
    })
  }

  const calculateDamage = () => {
    // Formules de dégâts améliorées basées sur les mécaniques du jeu
    const baseMeleeDamage = 100
    const baseSwordDamage = 150
    const baseGunDamage = 120
    const baseFruitDamage = 200

    // Multiplicateurs de stats (formule officielle)
    const meleeMultiplier = 1 + attacker.meleeStats * 0.003
    const swordMultiplier = 1 + attacker.swordStats * 0.003
    const gunMultiplier = 1 + attacker.gunStats * 0.003
    const fruitMultiplier = 1 + attacker.fruitStats * 0.003

    // Bonus de race
    const raceBonus = getRaceBonus(attacker.race)

    // Bonus d'arme et enhancement
    const weaponBonus = getWeaponBonus(attacker.weapon)
    const enhancementBonus = 1 + attacker.enhancement * 0.05 // +5% par niveau

    // Bonus de fruit
    const fruitBonus = getFruitBonus(attacker.fruit)

    // Haki bonus
    const hakiBonus = attacker.haki ? 1.1 : 1

    // Bonus de niveau (différence de niveau)
    const levelDifference = attacker.level - defender.level
    const levelBonus = Math.max(0.5, Math.min(2.0, 1 + levelDifference * 0.01))

    // Réduction de défense du défenseur
    const defenseReduction = calculateDefenseReduction()

    // Type de NPC multiplier
    const npcTypeData = npcTypes.find((t) => t.value === defender.npcType)
    const npcDefenseMultiplier = defender.type === "npc" ? npcTypeData?.defenseMultiplier || 1.0 : 1.0

    // Calculs des dégâts de base
    const meleeBase = baseMeleeDamage * meleeMultiplier * raceBonus.melee * hakiBonus * levelBonus
    const swordBase =
      baseSwordDamage * swordMultiplier * weaponBonus * enhancementBonus * raceBonus.sword * hakiBonus * levelBonus
    const gunBase = baseGunDamage * gunMultiplier * raceBonus.gun * hakiBonus * levelBonus
    const fruitBase = baseFruitDamage * fruitMultiplier * fruitBonus * raceBonus.fruit * hakiBonus * levelBonus

    // Application de la réduction de défense
    const finalDefenseReduction = defenseReduction * npcDefenseMultiplier

    // Calcul du taux de critique
    const criticalChance = Math.min(50, Math.max(0, (attacker.level - 1000) * 0.02)) // Max 50% à level 2550

    setResults({
      meleeBaseDamage: Math.round(meleeBase),
      meleeFinalDamage: Math.round(meleeBase * finalDefenseReduction),
      swordBaseDamage: Math.round(swordBase),
      swordFinalDamage: Math.round(swordBase * finalDefenseReduction),
      gunBaseDamage: Math.round(gunBase),
      gunFinalDamage: Math.round(gunBase * finalDefenseReduction),
      fruitBaseDamage: Math.round(fruitBase),
      fruitFinalDamage: Math.round(fruitBase * finalDefenseReduction),
      criticalChance: Math.round(criticalChance * 100) / 100,
      criticalMultiplier: 1.5 + attacker.enhancement * 0.02, // Enhancement augmente les critiques
    })
  }

  const calculateDefenseReduction = () => {
    const baseReduction = 1 - Math.min(0.8, defender.defenseStats * 0.0002)

    // Bonus de race défensif
    const defenderRaceBonus = getRaceBonus(defender.race)
    const raceDefenseBonus = 1 - (defenderRaceBonus.defense || 0) * 0.01

    return Math.max(0.1, baseReduction * raceDefenseBonus) // Minimum 10% de dégâts passent
  }

  const getRaceBonus = (race: string) => {
    const bonuses = {
      Human: { melee: 1.0, sword: 1.0, gun: 1.0, fruit: 1.0, defense: 0 },
      Angel: { melee: 1.0, sword: 1.125, gun: 1.0, fruit: 1.0, defense: 5 },
      Shark: { melee: 1.125, sword: 1.0, gun: 1.0, fruit: 1.0, defense: 10 },
      Cyborg: { melee: 1.0, sword: 1.0, gun: 1.125, fruit: 1.0, defense: 5 },
      Ghoul: { melee: 1.0, sword: 1.0, gun: 1.0, fruit: 1.125, defense: 0 },
      Mink: { melee: 1.0625, sword: 1.0625, gun: 1.0625, fruit: 1.0625, defense: 8 },
    }
    return bonuses[race as keyof typeof bonuses] || bonuses.Human
  }

  const getWeaponBonus = (weapon: string) => {
    const bonuses = {
      "Cursed Dual Katana": 2.2,
      "Dark Blade": 2.0,
      "True Triple Katana": 1.9,
      Yama: 1.8,
      Tushita: 1.8,
      Enma: 1.7,
      Shisui: 1.6,
      Wando: 1.5,
      Canvander: 1.4,
      "Buddy Sword": 1.3,
      Saber: 1.2,
      Bisento: 1.6,
      "Pole (1st Form)": 1.4,
      Rengoku: 1.5,
      "Soul Cane": 1.3,
    }
    return bonuses[weapon as keyof typeof bonuses] || 1.0
  }

  const getFruitBonus = (fruit: string) => {
    const bonuses = {
      "Dragon-Dragon": 2.5,
      "Leopard-Leopard": 2.6,
      "Spirit-Spirit": 2.3,
      "Control-Control": 2.2,
      "Venom-Venom": 2.1,
      "Dough-Dough": 2.0,
      "Shadow-Shadow": 1.9,
      "Buddha-Buddha": 1.8,
      "Phoenix-Phoenix": 1.7,
      "Blizzard-Blizzard": 1.6,
      "Rumble-Rumble": 1.5,
      "Paw-Paw": 1.4,
      "Gravity-Gravity": 1.6,
      "Mammoth-Mammoth": 1.5,
      "T-Rex-T-Rex": 1.4,
      "Portal-Portal": 1.7,
      "Pain-Pain": 1.8,
      "Kitsune-Kitsune": 1.9,
    }
    return bonuses[fruit as keyof typeof bonuses] || 1.0
  }

  const resetCalculator = () => {
    setAttacker({
      level: 2550,
      meleeStats: 2550,
      swordStats: 2550,
      gunStats: 2550,
      fruitStats: 2550,
      defenseStats: 2550,
      fruit: "",
      weapon: "",
      race: "",
      haki: false,
      enhancement: 0,
    })
    setDefender({
      level: 2550,
      defenseStats: 2550,
      race: "",
      type: "player",
      npcType: "normal",
    })
  }

  const copyResults = () => {
    const text = `Blox Fruits Damage Calculator Results:
Melee: ${results.meleeFinalDamage.toLocaleString()}
Sword: ${results.swordFinalDamage.toLocaleString()}
Gun: ${results.gunFinalDamage.toLocaleString()}
Fruit: ${results.fruitFinalDamage.toLocaleString()}
Critical Chance: ${results.criticalChance}%`

    navigator.clipboard.writeText(text)
  }

  // Auto-calculate when inputs change
  useEffect(() => {
    if (autoCalculate) {
      calculateDamage()
    }
  }, [attacker, defender, autoCalculate])

  // Calculate on mount
  useEffect(() => {
    calculateDamage()
  }, [])

  const getHighestDamage = () => {
    const damages = [
      { type: "Melee", value: results.meleeFinalDamage },
      { type: "Sword", value: results.swordFinalDamage },
      { type: "Gun", value: results.gunFinalDamage },
      { type: "Fruit", value: results.fruitFinalDamage },
    ]
    return damages.reduce((max, current) => (current.value > max.value ? current : max))
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="font-gaming text-3xl font-bold mb-2 flex items-center gap-2">
          <Zap className="w-8 h-8 text-primary" />
          Advanced Damage Calculator
        </h1>
        <p className="text-muted-foreground">Calculate precise damage output with enhanced formulas and race bonuses</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card className="gaming-card text-center">
          <CardContent className="pt-6">
            <Swords className="w-8 h-8 mx-auto mb-2 text-primary" />
            <div className="font-mono text-xl font-bold">{getHighestDamage().value.toLocaleString()}</div>
            <div className="text-sm text-muted-foreground">Highest Damage ({getHighestDamage().type})</div>
          </CardContent>
        </Card>
        <Card className="gaming-card text-center">
          <CardContent className="pt-6">
            <Target className="w-8 h-8 mx-auto mb-2 text-green-400" />
            <div className="font-mono text-xl font-bold">{results.criticalChance}%</div>
            <div className="text-sm text-muted-foreground">Critical Chance</div>
          </CardContent>
        </Card>
        <Card className="gaming-card text-center">
          <CardContent className="pt-6">
            <Zap className="w-8 h-8 mx-auto mb-2 text-yellow-400" />
            <div className="font-mono text-xl font-bold">{results.criticalMultiplier.toFixed(1)}x</div>
            <div className="text-sm text-muted-foreground">Critical Multiplier</div>
          </CardContent>
        </Card>
        <Card className="gaming-card text-center">
          <CardContent className="pt-6">
            <Shield className="w-8 h-8 mx-auto mb-2 text-blue-400" />
            <div className="font-mono text-xl font-bold">{Math.round((1 - calculateDefenseReduction()) * 100)}%</div>
            <div className="text-sm text-muted-foreground">Damage Reduction</div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Attacker Configuration */}
        <Card className="gaming-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Swords className="w-5 h-5" />
              Attacker Setup
            </CardTitle>
            <CardDescription>Configure the attacking character</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="attacker-level">Level (1-2550)</Label>
                <Input
                  id="attacker-level"
                  type="number"
                  min="1"
                  max="2550"
                  value={attacker.level}
                  onChange={(e) => handleAttackerChange("level", e.target.value)}
                  className="font-mono"
                />
              </div>
              <div>
                <Label htmlFor="attacker-race">Race</Label>
                <Select value={attacker.race} onValueChange={(value) => handleAttackerChange("race", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select race" />
                  </SelectTrigger>
                  <SelectContent>
                    {races.map((race) => (
                      <SelectItem key={race} value={race}>
                        {race}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="attacker-fruit">Devil Fruit</Label>
              <Select value={attacker.fruit} onValueChange={(value) => handleAttackerChange("fruit", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select fruit" />
                </SelectTrigger>
                <SelectContent>
                  {fruits.map((fruit) => (
                    <SelectItem key={fruit} value={fruit}>
                      {fruit}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="attacker-weapon">Weapon</Label>
              <Select value={attacker.weapon} onValueChange={(value) => handleAttackerChange("weapon", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select weapon" />
                </SelectTrigger>
                <SelectContent>
                  {weapons.map((weapon) => (
                    <SelectItem key={weapon} value={weapon}>
                      {weapon}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {showAdvanced && (
              <div>
                <Label htmlFor="enhancement">Enhancement Level (0-20)</Label>
                <Input
                  id="enhancement"
                  type="number"
                  min="0"
                  max="20"
                  value={attacker.enhancement}
                  onChange={(e) => handleAttackerChange("enhancement", e.target.value)}
                  className="font-mono"
                />
              </div>
            )}

            <Separator />

            <div className="space-y-3">
              <h4 className="font-semibold">Stats Distribution</h4>
              <div className="grid grid-cols-1 gap-3">
                {["meleeStats", "swordStats", "gunStats", "fruitStats", "defenseStats"].map((stat) => (
                  <div key={stat}>
                    <Label htmlFor={stat} className="capitalize">
                      {stat.replace("Stats", "")} (0-2550)
                    </Label>
                    <Input
                      id={stat}
                      type="number"
                      min="0"
                      max="2550"
                      value={attacker[stat as keyof typeof attacker] as number}
                      onChange={(e) => handleAttackerChange(stat, e.target.value)}
                      className="font-mono"
                    />
                  </div>
                ))}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Switch
                  id="haki"
                  checked={attacker.haki}
                  onCheckedChange={(checked: boolean) => handleAttackerChange("haki", checked)}
                />
                <Label htmlFor="haki">Haki Enabled (+10%)</Label>
              </div>
              <Button variant="outline" size="sm" onClick={() => setShowAdvanced(!showAdvanced)}>
                Advanced
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Defender Configuration */}
        <Card className="gaming-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Defender Setup
            </CardTitle>
            <CardDescription>Configure the defending target</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="defender-type">Target Type</Label>
              <Select value={defender.type} onValueChange={(value) => handleDefenderChange("type", value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="player">Player</SelectItem>
                  <SelectItem value="npc">NPC</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {defender.type === "npc" && (
              <div>
                <Label htmlFor="npc-type">NPC Type</Label>
                <Select value={defender.npcType} onValueChange={(value) => handleDefenderChange("npcType", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {npcTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="defender-level">Level (1-2550)</Label>
                <Input
                  id="defender-level"
                  type="number"
                  min="1"
                  max="2550"
                  value={defender.level}
                  onChange={(e) => handleDefenderChange("level", e.target.value)}
                  className="font-mono"
                />
              </div>
              <div>
                <Label htmlFor="defender-defense">Defense Stats (0-2550)</Label>
                <Input
                  id="defender-defense"
                  type="number"
                  min="0"
                  max="2550"
                  value={defender.defenseStats}
                  onChange={(e) => handleDefenderChange("defenseStats", e.target.value)}
                  className="font-mono"
                />
              </div>
            </div>

            {defender.type === "player" && (
              <div>
                <Label htmlFor="defender-race">Race</Label>
                <Select value={defender.race} onValueChange={(value) => handleDefenderChange("race", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select race" />
                  </SelectTrigger>
                  <SelectContent>
                    {races.map((race) => (
                      <SelectItem key={race} value={race}>
                        {race}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="flex items-center space-x-2">
              <Switch id="auto-calculate" checked={autoCalculate} onCheckedChange={setAutoCalculate} />
              <Label htmlFor="auto-calculate" className="text-sm">
                Auto-calculate
              </Label>
            </div>

            <div className="flex gap-2">
              <Button onClick={calculateDamage} className="flex-1 glow-effect">
                <Calculator className="w-4 h-4 mr-2" />
                Calculate
              </Button>
              <Button onClick={resetCalculator} variant="outline" size="sm">
                <RotateCcw className="w-4 h-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Results */}
        <Card className="gaming-card">
          <CardHeader>
            <CardTitle>Damage Results</CardTitle>
            <CardDescription>Calculated damage output with all bonuses</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="summary" className="space-y-4">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="detailed">Detailed</TabsTrigger>
              </TabsList>

              <TabsContent value="summary" className="space-y-4">
                <div className="space-y-3">
                  {[
                    { name: "Melee", damage: results.meleeFinalDamage, icon: Swords, color: "text-red-400" },
                    { name: "Sword", damage: results.swordFinalDamage, icon: Swords, color: "text-blue-400" },
                    { name: "Gun", damage: results.gunFinalDamage, icon: Target, color: "text-green-400" },
                    { name: "Fruit", damage: results.fruitFinalDamage, icon: Zap, color: "text-purple-400" },
                  ].map((item) => (
                    <div key={item.name} className="flex justify-between items-center p-3 bg-secondary/20 rounded-lg">
                      <div className="flex items-center gap-2">
                        <item.icon className={`w-4 h-4 ${item.color}`} />
                        <span className="font-semibold">{item.name}:</span>
                      </div>
                      <div className="text-right">
                        <div className="font-mono text-lg font-bold text-primary">{item.damage.toLocaleString()}</div>
                        {results.criticalChance > 0 && (
                          <div className="text-xs text-muted-foreground">
                            Crit: {Math.round(item.damage * results.criticalMultiplier).toLocaleString()}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                {results.criticalChance > 0 && (
                  <div className="p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                    <div className="flex items-center gap-2 text-yellow-400 font-semibold mb-1">
                      <Target className="w-4 h-4" />
                      Critical Hit Info
                    </div>
                    <div className="text-sm space-y-1">
                      <div>Chance: {results.criticalChance}%</div>
                      <div>Multiplier: {results.criticalMultiplier.toFixed(1)}x</div>
                    </div>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="detailed" className="space-y-4">
                <div className="space-y-4">
                  {[
                    { name: "Melee", base: results.meleeBaseDamage, final: results.meleeFinalDamage },
                    { name: "Sword", base: results.swordBaseDamage, final: results.swordFinalDamage },
                    { name: "Gun", base: results.gunBaseDamage, final: results.gunFinalDamage },
                    { name: "Fruit", base: results.fruitBaseDamage, final: results.fruitFinalDamage },
                  ].map((item) => (
                    <div key={item.name}>
                      <h4 className="font-semibold mb-2">{item.name} Combat</h4>
                      <div className="text-sm space-y-1 bg-secondary/20 p-3 rounded-lg">
                        <div className="flex justify-between">
                          <span>Base Damage:</span>
                          <span className="font-mono">{item.base.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>After Defense:</span>
                          <span className="font-mono text-primary">{item.final.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Damage Reduction:</span>
                          <span className="font-mono text-red-400">
                            -{Math.round(((item.base - item.final) / item.base) * 100)}%
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>
            </Tabs>

            <div className="flex gap-2 mt-4">
              <Button onClick={copyResults} variant="outline" className="flex-1 bg-transparent">
                <Copy className="w-4 h-4 mr-2" />
                Copy Results
              </Button>
              <Button variant="outline" className="flex-1 bg-transparent">
                <Share2 className="w-4 h-4 mr-2" />
                Share Build
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Warnings */}
      {(attacker.level < 100 || defender.level < 100) && (
        <Card className="gaming-card mt-6 bg-yellow-500/5 border-yellow-500/20">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5" />
              <div>
                <h3 className="font-semibold text-yellow-400 mb-1">Low Level Warning</h3>
                <p className="text-sm text-muted-foreground">
                  Damage calculations are most accurate for levels 100+. Low level results may vary significantly.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
