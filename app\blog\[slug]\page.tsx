"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { AdBanner } from "@/components/ads/ad-banner"
import { Calendar, Eye, Heart, MessageCircle, Share2, Bookmark, ArrowLeft, Clock, Tag } from "lucide-react"
import Link from "next/link"

interface BlogPost {
  _id: string
  title: string
  slug: string
  content: string
  author: {
    name: string
    avatar: string
    bio: string
  }
  category: string
  tags: string[]
  publishedAt: string
  updatedAt: string
  views: number
  likes: number
  comments: number
  readTime: number
  featuredImage?: string
}

interface Comment {
  _id: string
  author: {
    name: string
    avatar: string
  }
  content: string
  publishedAt: string
  likes: number
}

export default function BlogPostPage({ params }: { params: { slug: string } }) {
  const [post, setPost] = useState<BlogPost | null>(null)
  const [comments, setComments] = useState<Comment[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isLiked, setIsLiked] = useState(false)
  const [isBookmarked, setIsBookmarked] = useState(false)

  // Mock data pour la démonstration
  const mockPost: BlogPost = {
    _id: "1",
    title: "Complete Guide to Blox Fruits Trading in 2024",
    slug: "complete-guide-blox-fruits-trading-2024",
    content: `
# Complete Guide to Blox Fruits Trading in 2024

Trading in Blox Fruits has evolved significantly over the years, and 2024 brings new opportunities and challenges for traders. This comprehensive guide will help you master the art of trading and maximize your profits.

## Understanding Trading Values

The foundation of successful trading lies in understanding current market values. Here are the key factors that influence fruit values:

### Rarity and Demand
- **Mythical Fruits**: Leopard, Dragon, Kitsune - These command the highest prices
- **Legendary Fruits**: Spirit, Control, Venom - Solid mid-tier options
- **Rare Fruits**: Buddha, Phoenix, Portal - Great for beginners

### Market Trends
Values fluctuate based on:
- Game updates and balancing changes
- Community perception and meta shifts
- Seasonal events and limited-time offers

## Trading Strategies

### 1. The Value Trading Method
Focus on trading items of equal or slightly higher value. This conservative approach minimizes risk while ensuring steady progress.

### 2. The Demand Trading Method
Target high-demand items even if they're slightly overvalued. These items are easier to trade later.

### 3. The Investment Method
Hold onto items that are likely to increase in value due to upcoming updates or meta changes.

## Common Trading Mistakes

- **Emotional Trading**: Don't get attached to specific fruits
- **Ignoring Market Trends**: Stay updated with community discussions
- **Rushing Trades**: Take time to evaluate each offer
- **Not Diversifying**: Don't put all your value in one item

## Advanced Tips

### Building Reputation
- Complete trades as promised
- Be honest about item conditions
- Help newer traders learn the ropes

### Market Analysis
- Monitor Discord servers and Reddit communities
- Track price movements over time
- Understand seasonal patterns

## Conclusion

Successful trading in Blox Fruits requires patience, knowledge, and strategic thinking. Start small, learn from each trade, and gradually build your way up to the most valuable items in the game.

Remember: The best traders are those who help build a positive trading community for everyone.
    `,
    author: {
      name: "BloxMaster",
      avatar: "/placeholder.svg?height=60&width=60",
      bio: "Professional Blox Fruits trader with 3+ years of experience. Helping players master the trading game.",
    },
    category: "Trading",
    tags: ["trading", "guide", "values", "tips", "strategy"],
    publishedAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-15T10:00:00Z",
    views: 15420,
    likes: 892,
    comments: 156,
    readTime: 8,
    featuredImage: "/placeholder.svg?height=300&width=800",
  }

  const mockComments: Comment[] = [
    {
      _id: "1",
      author: {
        name: "TradingPro",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      content: "Excellent guide! The value trading method really helped me get started. Thanks for sharing!",
      publishedAt: "2024-01-15T12:00:00Z",
      likes: 23,
    },
    {
      _id: "2",
      author: {
        name: "NewTrader",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      content:
        "As a beginner, this guide is exactly what I needed. The common mistakes section saved me from making bad trades.",
      publishedAt: "2024-01-15T14:30:00Z",
      likes: 15,
    },
  ]

  useEffect(() => {
    // Simuler le chargement du post
    setIsLoading(true)
    setTimeout(() => {
      setPost(mockPost)
      setComments(mockComments)
      setIsLoading(false)
    }, 1000)
  }, [params.slug])

  const handleLike = () => {
    setIsLiked(!isLiked)
    if (post) {
      setPost({
        ...post,
        likes: isLiked ? post.likes - 1 : post.likes + 1,
      })
    }
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: post?.title,
        url: window.location.href,
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <div className="animate-pulse">
            <div className="h-8 bg-slate-600 rounded mb-4 w-3/4"></div>
            <div className="h-4 bg-slate-600 rounded mb-8 w-1/2"></div>
            <div className="h-64 bg-slate-600 rounded mb-8"></div>
            <div className="space-y-4">
              {[...Array(10)].map((_, i) => (
                <div key={i} className="h-4 bg-slate-600 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!post) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="container mx-auto px-4 py-8 max-w-4xl">
          <Card className="bg-slate-800/50 border-slate-700 text-center py-12">
            <CardContent>
              <h1 className="text-2xl font-bold text-white mb-4">Article Not Found</h1>
              <p className="text-gray-400 mb-6">The article you're looking for doesn't exist.</p>
              <Link href="/blog">
                <Button className="bg-primary hover:bg-primary/90">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Blog
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Back Button */}
        <div className="mb-6">
          <Link href="/blog">
            <Button variant="outline" className="border-slate-600 text-gray-300 hover:bg-slate-700 bg-transparent">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Blog
            </Button>
          </Link>
        </div>

        {/* Article Header */}
        <Card className="bg-slate-800/50 border-slate-700 mb-8">
          <CardContent className="p-8">
            <div className="flex items-center gap-2 mb-4">
              <Badge variant="outline" className="border-slate-600 text-gray-300">
                {post.category}
              </Badge>
              <div className="flex items-center gap-1 text-sm text-gray-400">
                <Clock className="w-3 h-3" />
                {post.readTime} min read
              </div>
            </div>

            <h1 className="text-4xl font-bold text-white mb-6">{post.title}</h1>

            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <img
                  src={post.author.avatar || "/placeholder.svg"}
                  alt={post.author.name}
                  className="w-12 h-12 rounded-full"
                />
                <div>
                  <div className="font-semibold text-white">{post.author.name}</div>
                  <div className="text-sm text-gray-400">{post.author.bio}</div>
                </div>
              </div>
              <div className="flex items-center gap-4 text-sm text-gray-400">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {new Date(post.publishedAt).toLocaleDateString()}
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="w-4 h-4" />
                  {post.views.toLocaleString()}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handleLike}
                className={`border-slate-600 ${isLiked ? "text-red-400 border-red-400" : "text-gray-300"} hover:bg-slate-700`}
              >
                <Heart className={`w-4 h-4 mr-2 ${isLiked ? "fill-current" : ""}`} />
                {post.likes}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsBookmarked(!isBookmarked)}
                className={`border-slate-600 ${isBookmarked ? "text-yellow-400 border-yellow-400" : "text-gray-300"} hover:bg-slate-700`}
              >
                <Bookmark className={`w-4 h-4 mr-2 ${isBookmarked ? "fill-current" : ""}`} />
                Save
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleShare}
                className="border-slate-600 text-gray-300 hover:bg-slate-700 bg-transparent"
              >
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Featured Image */}
        {post.featuredImage && (
          <div className="mb-8">
            <div className="h-64 bg-gradient-to-r from-primary/20 to-purple-600/20 rounded-lg"></div>
          </div>
        )}

        {/* Article Content */}
        <Card className="bg-slate-800/50 border-slate-700 mb-8">
          <CardContent className="p-8">
            <div className="prose prose-invert max-w-none">
              <div className="text-gray-300 leading-relaxed whitespace-pre-line">{post.content}</div>
            </div>
          </CardContent>
        </Card>

        {/* Tags */}
        <Card className="bg-slate-800/50 border-slate-700 mb-8">
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-3">
              <Tag className="w-4 h-4 text-gray-400" />
              <span className="text-sm font-medium text-gray-400">Tags</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {post.tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="bg-slate-700 text-gray-300">
                  {tag}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Ad Banner */}
        <div className="mb-8">
          <AdBanner adSlot="blog-article" adFormat="horizontal" />
        </div>

        {/* Comments Section */}
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-white">
              <MessageCircle className="w-5 h-5" />
              Comments ({post.comments})
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-6">
              {comments.map((comment) => (
                <div key={comment._id} className="flex gap-4">
                  <img
                    src={comment.author.avatar || "/placeholder.svg"}
                    alt={comment.author.name}
                    className="w-10 h-10 rounded-full"
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-semibold text-white">{comment.author.name}</span>
                      <span className="text-sm text-gray-400">
                        {new Date(comment.publishedAt).toLocaleDateString()}
                      </span>
                    </div>
                    <p className="text-gray-300 mb-2">{comment.content}</p>
                    <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                      <Heart className="w-3 h-3 mr-1" />
                      {comment.likes}
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            <Separator className="my-6 bg-slate-600" />

            <div className="text-center">
              <p className="text-gray-400 mb-4">Want to join the discussion?</p>
              <Button className="bg-primary hover:bg-primary/90">Sign in to Comment</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
