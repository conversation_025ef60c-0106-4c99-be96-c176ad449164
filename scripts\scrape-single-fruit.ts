import * as dotenv from "dotenv";
import { MongoClient } from "mongodb";
import { EnhancedFruitScraper } from "../lib/scrapers/enhanced-fruit-scraper";

// Charge les variables d'environnement (.env.local ou variables système)
dotenv.config({ path: ".env.local" });

async function scrapeSingleFruit(fruitName: string) {
  const mongoUrl = process.env.MONGODB_URL;
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required");
    process.exit(1);
  }

  const client = new MongoClient(mongoUrl);
  const fruitScraper = new EnhancedFruitScraper();

  try {
    await client.connect();
    const db = client.db("bloxfruits");
    const fruitsCollection = db.collection("fruits");

    console.log(`🍎 Scraping fruit: ${fruitName}`);
    const item = await fruitScraper.scrapeItem(fruitName, "fruit");

    if (!item) {
      console.warn("⚠️ Nothing scraped, aborting");
      return;
    }

    // Upsert dans la collection
    await fruitsCollection.updateOne({ name: fruitName }, { $set: item }, { upsert: true });
    console.log(`✅ Successfully saved '${fruitName}' to MongoDB`);
  } catch (error) {
    console.error("❌ Error scraping single fruit:", error);
  } finally {
    await client.close();
  }
}

// Lancer avec "npx ts-node scripts/scrape-single-fruit.ts" ou via package.json
const FRUITS = ["Yeti", "Dragon", "Gravity"];

// Exécute les scrapes en série afin de limiter la charge réseau / DB
(async () => {
  for (const fruit of FRUITS) {
    await scrapeSingleFruit(fruit).catch(console.error);
  }
})(); 