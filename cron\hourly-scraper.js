#!/usr/bin/env node

// Script cron pour exécuter le scraper toutes les heures
// Ajouter à crontab: 0 * * * * /path/to/node /path/to/hourly-scraper.js

const { spawn } = require("child_process")
const path = require("path")

const projectRoot = path.dirname(__dirname)
const scraperScript = path.join(projectRoot, "scripts", "run-scraper.ts")

console.log(`[${new Date().toISOString()}] Starting hourly scraper...`)

const scraper = spawn("npx", ["tsx", scraperScript], {
  cwd: projectRoot,
  stdio: "inherit",
})

scraper.on("close", (code) => {
  if (code === 0) {
    console.log(`[${new Date().toISOString()}] Hourly scraper completed successfully`)
  } else {
    console.error(`[${new Date().toISOString()}] Hourly scraper failed with code ${code}`)
  }
})

scraper.on("error", (error) => {
  console.error(`[${new Date().toISOString()}] Hourly scraper error:`, error)
})
