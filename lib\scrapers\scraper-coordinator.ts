import { MongoClient } from "mongodb"
import { MaterialScraper } from "./scrapers/material-scraper"
import { WeaponScraper } from "./scrapers/weapon-scraper"
import { AccessoryScraper } from "./scrapers/accessory-scraper"
import { NPCScraper } from "./scrapers/npc-scraper"
import { QuestScraper } from "./scrapers/quest-scraper"
import { EnemyScraper } from "./scrapers/enemy-scraper"
import { MechanicScraper } from "./scrapers/mechanic-scraper"
import { ScrapedItem, CategoryConfig } from "./types"
import { EnhancedFruitScraper } from "./enhanced-fruit-scraper"

export class ScraperCoordinator {
  private mongoClient: MongoClient
  private fruitScraper: EnhancedFruitScraper;
  private materialScraper: MaterialScraper
  private weaponScraper: WeaponScraper
  private accessoryScraper: AccessoryScraper
  private npcScraper: NPCScraper
  private questScraper: QuestScraper
  private enemyScraper: EnemyScraper
  private mechanicScraper: MechanicScraper

  constructor(mongoUrl: string) {
    this.mongoClient = new MongoClient(mongoUrl)
    this.fruitScraper = new EnhancedFruitScraper();
    this.materialScraper = new MaterialScraper()
    this.weaponScraper = new WeaponScraper()
    this.accessoryScraper = new AccessoryScraper()
    this.npcScraper = new NPCScraper()
    this.questScraper = new QuestScraper()
    this.enemyScraper = new EnemyScraper()
    this.mechanicScraper = new MechanicScraper()
  }

  private async saveToDatabase(items: ScrapedItem[], collection: string): Promise<void> {
    if (items.length === 0) {
      console.warn(`⚠️ No items to save for ${collection}`)
      return
    }

    try {
      const db = this.mongoClient.db("bloxfruits")
      const coll = db.collection(collection)
      
      // Clear existing data and insert new data
      await coll.deleteMany({})
      await coll.insertMany(items)
      
      console.log(`💾 Saved ${items.length} items to ${collection} collection`)
    } catch (error) {
      console.error(`❌ Error saving to ${collection}:`, error)
      throw error
    }
  }

  async scrapeFruits(): Promise<ScrapedItem[]> {
    console.log("\n🍎 === SCRAPING FRUITS ===")
    try {
      const items = await this.fruitScraper.scrapeCategory("Blox_Fruits")
      await this.saveToDatabase(items, "fruits")
      return items
    } catch (error) {
      console.error("❌ Error scraping fruits:", error)
      return []
    }
  }

  async scrapeMaterials(): Promise<ScrapedItem[]> {
    console.log("\n🧱 === SCRAPING MATERIALS ===")
    try {
      const items = await this.materialScraper.scrapeCategory("Materials")
      await this.saveToDatabase(items, "materials")
      return items
    } catch (error) {
      console.error("❌ Error scraping materials:", error)
      return []
    }
  }

  async scrapeSwords(): Promise<ScrapedItem[]> {
    console.log("\n⚔️ === SCRAPING SWORDS ===")
    try {
      const items = await this.weaponScraper.scrapeCategory("Swords", "sword")
      await this.saveToDatabase(items, "swords")
      return items
    } catch (error) {
      console.error("❌ Error scraping swords:", error)
      return []
    }
  }

  async scrapeGuns(): Promise<ScrapedItem[]> {
    console.log("\n🔫 === SCRAPING GUNS ===")
    try {
      const items = await this.weaponScraper.scrapeCategory("Guns", "gun")
      await this.saveToDatabase(items, "guns")
      return items
    } catch (error) {
      console.error("❌ Error scraping guns:", error)
      return []
    }
  }

  async scrapeAccessories(): Promise<ScrapedItem[]> {
    console.log("\n💍 === SCRAPING ACCESSORIES ===")
    try {
      const items = await this.accessoryScraper.scrapeCategory("Accessories")
      await this.saveToDatabase(items, "accessories")
      return items
    } catch (error) {
      console.error("❌ Error scraping accessories:", error)
      return []
    }
  }

  async scrapeNPCs(): Promise<ScrapedItem[]> {
    console.log("\n👤 === SCRAPING NPCS ===")
    try {
      const items = await this.npcScraper.scrapeCategory("NPCs")
      await this.saveToDatabase(items, "npcs")
      return items
    } catch (error) {
      console.error("❌ Error scraping NPCs:", error)
      return []
    }
  }

  async scrapeQuests(): Promise<ScrapedItem[]> {
    console.log("\n📋 === SCRAPING QUESTS ===")
    try {
      const items = await this.questScraper.scrapeCategory("Quests")
      await this.saveToDatabase(items, "quests")
      return items
    } catch (error) {
      console.error("❌ Error scraping quests:", error)
      return []
    }
  }

  async scrapeEnemies(): Promise<ScrapedItem[]> {
    console.log("\n👹 === SCRAPING ENEMIES/RAIDS ===")
    try {
      const items = await this.enemyScraper.scrapeCategory("Raids")
      await this.saveToDatabase(items, "raids")
      return items
    } catch (error) {
      console.error("❌ Error scraping enemies:", error)
      return []
    }
  }

  async scrapeMechanics(): Promise<ScrapedItem[]> {
    console.log("\n⚙️ === SCRAPING MECHANICS ===")
    try {
      const items = await this.mechanicScraper.scrapeCategory("Game_Mechanics")
      await this.saveToDatabase(items, "mechanics")
      return items
    } catch (error) {
      console.error("❌ Error scraping mechanics:", error)
      return []
    }
  }

  async scrapeCategory(categoryConfig: CategoryConfig): Promise<ScrapedItem[]> {
    console.log(`\n🎯 === SCRAPING ${categoryConfig.name.toUpperCase()} ===`)
    
    try {
      let items: ScrapedItem[] = []

      switch (categoryConfig.type) {
        case "fruit":
          items = await this.fruitScraper.scrapeCategory(categoryConfig.name)
          break
        case "material":
          items = await this.materialScraper.scrapeCategory(categoryConfig.name)
          break
        case "sword":
          items = await this.weaponScraper.scrapeCategory(categoryConfig.name, "sword")
          break
        case "gun":
          items = await this.weaponScraper.scrapeCategory(categoryConfig.name, "gun")
          break
        case "accessory":
          items = await this.accessoryScraper.scrapeCategory(categoryConfig.name)
          break
        case "npc":
          items = await this.npcScraper.scrapeCategory(categoryConfig.name)
          break
        case "quest":
          items = await this.questScraper.scrapeCategory(categoryConfig.name)
          break
        case "enemy":
        case "raid":
          items = await this.enemyScraper.scrapeCategory(categoryConfig.name)
          break
        case "mechanic":
          items = await this.mechanicScraper.scrapeCategory(categoryConfig.name)
          break
        default:
          console.warn(`⚠️ Unknown category type: ${categoryConfig.type}`)
          return []
      }

      await this.saveToDatabase(items, categoryConfig.collection)
      return items
    } catch (error) {
      console.error(`❌ Error scraping category ${categoryConfig.name}:`, error)
      return []
    }
  }

  async scrapeAll(): Promise<void> {
    console.log("🚀 Starting comprehensive scraping of all categories...")

    await this.mongoClient.connect()

    const categories: CategoryConfig[] = [
      { name: "Blox_Fruits", type: "fruit", collection: "fruits" },
      { name: "Materials", type: "material", collection: "materials" },
      { name: "Swords", type: "sword", collection: "swords" },
      { name: "Guns", type: "gun", collection: "guns" },
      { name: "Accessories", type: "accessory", collection: "accessories" },
      { name: "NPCs", type: "npc", collection: "npcs" },
      { name: "Quests", type: "quest", collection: "quests" },
      { name: "Raids", type: "raid", collection: "raids" },
      { name: "Game_Mechanics", type: "mechanic", collection: "mechanics" },
    ]

    const results: { [key: string]: number } = {}

    for (const category of categories) {
      try {
        const items = await this.scrapeCategory(category)
        results[category.name] = items.length

        // Pause between categories
        console.log("⏸️ Pausing 5 seconds before next category...")
        await new Promise((resolve) => setTimeout(resolve, 5000))
      } catch (error) {
        console.error(`❌ Error processing category ${category.name}:`, error)
        results[category.name] = 0
      }
    }

    await this.mongoClient.close()

    // Print summary
    console.log("\n🎉 === SCRAPING COMPLETED ===")
    console.log("📊 Summary:")
    Object.entries(results).forEach(([category, count]) => {
      console.log(`  ${category}: ${count} items`)
    })
    
    const total = Object.values(results).reduce((sum, count) => sum + count, 0)
    console.log(`📈 Total items scraped: ${total}`)
  }

  async scrapeSpecific(categories: string[]): Promise<void> {
    console.log(`🎯 Starting scraping of specific categories: ${categories.join(", ")}`)

    await this.mongoClient.connect()

    const categoryConfigs: CategoryConfig[] = [
      { name: "Blox_Fruits", type: "fruit", collection: "fruits" },
      { name: "Materials", type: "material", collection: "materials" },
      { name: "Swords", type: "sword", collection: "swords" },
      { name: "Guns", type: "gun", collection: "guns" },
      { name: "Accessories", type: "accessory", collection: "accessories" },
      { name: "NPCs", type: "npc", collection: "npcs" },
      { name: "Quests", type: "quest", collection: "quests" },
      { name: "Raids", type: "raid", collection: "raids" },
      { name: "Game_Mechanics", type: "mechanic", collection: "mechanics" },
    ]

    const results: { [key: string]: number } = {}

    for (const categoryName of categories) {
      const categoryConfig = categoryConfigs.find(c => 
        c.name.toLowerCase() === categoryName.toLowerCase() ||
        c.type.toLowerCase() === categoryName.toLowerCase() ||
        c.collection.toLowerCase() === categoryName.toLowerCase()
      )

      if (!categoryConfig) {
        console.warn(`⚠️ Unknown category: ${categoryName}`)
        continue
      }

      try {
        const items = await this.scrapeCategory(categoryConfig)
        results[categoryConfig.name] = items.length

        // Pause between categories
        if (categories.length > 1) {
          console.log("⏸️ Pausing 3 seconds before next category...")
          await new Promise((resolve) => setTimeout(resolve, 3000))
        }
      } catch (error) {
        console.error(`❌ Error processing category ${categoryConfig.name}:`, error)
        results[categoryConfig.name] = 0
      }
    }

    await this.mongoClient.close()

    // Print summary
    console.log("\n🎉 === SCRAPING COMPLETED ===")
    console.log("📊 Summary:")
    Object.entries(results).forEach(([category, count]) => {
      console.log(`  ${category}: ${count} items`)
    })
    
    const total = Object.values(results).reduce((sum, count) => sum + count, 0)
    console.log(`📈 Total items scraped: ${total}`)
  }

  async close(): Promise<void> {
    await this.mongoClient.close()
  }
}
