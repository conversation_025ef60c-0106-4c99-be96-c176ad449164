import * as fs from "fs";
import * as path from "path";

function printKeys(obj: any, prefix: string = "", lines: string[] = []) {
  if (Array.isArray(obj)) {
    if (obj.length > 0) {
      printKeys(obj[0], prefix + "[].", lines);
    }
    return;
  }
  if (typeof obj === "object" && obj !== null) {
    for (const key of Object.keys(obj)) {
      lines.push(prefix + key);
      printKeys(obj[key], prefix + key + ".", lines);
    }
  }
}

const file = process.argv[2];
if (!file) {
  console.error("Usage: ts-node print-json-keys.ts <jsonFile> [outputFile]");
  process.exit(1);
}
const filePath = path.resolve(file);
const raw = fs.readFileSync(filePath, "utf-8");
const json = JSON.parse(raw);

const lines: string[] = [];
printKeys(json, "", lines);

const outputFile = process.argv[3];
if (outputFile) {
  fs.writeFileSync(outputFile, lines.join("\n"), "utf-8");
} else {
  for (const line of lines) {
    console.log(line);
  }
}
