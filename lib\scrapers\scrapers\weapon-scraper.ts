import { BaseScraper } from "../base-scraper"
import { ScrapedItem, WeaponData } from "../types"

export class WeaponScraper extends BaseScraper {
  
  extractSpecificData(wikitext: string, infoboxData: Record<string, string>): WeaponData {
    const weaponData: any = {}

    // Extract weapon type
    if (infoboxData.type) {
      const type = infoboxData.type.trim()
      if (type === "Sword" || type === "Gun") {
        weaponData.weaponType = type as "Sword" | "Gun"
      }
    }

    // Extract damage
    if (infoboxData.damage) {
      const damage = this.parseNumber(infoboxData.damage)
      if (damage) weaponData.damage = damage
    }

    // Extract mastery required
    if (infoboxData.mastery || infoboxData.mastery_required || infoboxData.masteryreq) {
      const mastery = this.parseNumber(infoboxData.mastery || infoboxData.mastery_required || infoboxData.masteryreq)
      if (mastery) weaponData.masteryRequired = mastery
    }

    // Extract price
    if (infoboxData.price) {
      weaponData.price = infoboxData.price
    } else {
      // Look for price in wikitext
      const priceMatch = wikitext.match(/\{\{Money\|([^}]+)\}\}/)
      if (priceMatch) {
        weaponData.price = priceMatch[1]
      }
    }

    // Extract drop chance
    if (infoboxData.chance) {
      const chance = this.parseNumber(infoboxData.chance.replace('%', ''))
      if (chance) weaponData.dropChance = chance
    }

    // Detect version (V2, V3, etc.)
    if (wikitext.toLowerCase().includes("version 2") || wikitext.toLowerCase().includes("v2")) {
      weaponData.version = 2
    } else if (wikitext.toLowerCase().includes("version 3") || wikitext.toLowerCase().includes("v3")) {
      weaponData.version = 3
    } else {
      weaponData.version = 1
    }

    // Extract level requirement
    const levelReqMatch = wikitext.match(/at least Lv\.?\s*(\d+)/i)
    if (levelReqMatch) {
      const levelReq = parseInt(levelReqMatch[1])
      if (!isNaN(levelReq)) {
        weaponData.levelRequirement = levelReq
      }
    }

    // Extract stats from tables
    const statsMatch = wikitext.match(/Stats=([\s\S]*?)(?=\|-\||<\/tabber>)/i)
    if (statsMatch) {
      const stats: Array<{ move: string; damage: number; cooldown: number }> = []
      const statsContent = statsMatch[1]

      // Extract stats table rows
      const statRows = statsContent.match(/\{\{Stats Table Row\|([^}]+)\}\}/g)
      if (statRows) {
        statRows.forEach(row => {
          const parts = row.match(/\{\{Stats Table Row\|([^|]+)\|([^|]+)\|([^|]+)\|([^}]+)\}\}/)
          if (parts && parts.length >= 4) {
            const move = this.cleanWikitext(parts[2])
            const damage = this.parseNumber(parts[3])
            const cooldown = this.parseNumber(parts[4])

            if (move && damage && cooldown) {
              stats.push({ move, damage, cooldown })
            }
          }
        })
      }

      if (stats.length > 0) {
        weaponData.stats = stats
      }
    }

    // Extract upgrade requirements
    const upgradeMatch = wikitext.match(/==\s*Upgrading\s*==([\s\S]*?)(?===|$)/i)
    if (upgradeMatch) {
      const upgradeRequirements: Array<{ material: string; quantity: number }> = []

      // Look for material requirement patterns
      const materialMatches = upgradeMatch[1].match(/\{\{([^|]+)\|(\d+)\}\}/g)
      if (materialMatches) {
        materialMatches.forEach(match => {
          const materialMatch = match.match(/\{\{([^|]+)\|(\d+)\}\}/)
          if (materialMatch) {
            const material = materialMatch[1].trim()
            const quantity = parseInt(materialMatch[2])
            if (!isNaN(quantity)) {
              upgradeRequirements.push({ material, quantity })
            }
          }
        })
      }

      if (upgradeRequirements.length > 0) {
        weaponData.upgradeRequirements = upgradeRequirements
      }
    }

    // Extract pros and cons with better separation
    // Try multiple patterns to find Overview section
    let overviewContent = ""

    const overviewPatterns = [
      /==\s*Overview\s*==([\s\S]*?)(?===|$)/i,
      /\{\{Overview\|([\s\S]*?)\}\}/i,
      /Pros=([\s\S]*?)(?=Cons=|$)/i // Direct pattern for Saber
    ]

    for (const pattern of overviewPatterns) {
      const match = wikitext.match(pattern)
      if (match) {
        overviewContent = match[1]
        break
      }
    }

    if (overviewContent) {
      // Extract pros
      const prosMatch = overviewContent.match(/Pros=([\s\S]*?)(?=\|-\||Cons=|<\/tabber>|$)/i)
      if (prosMatch) {
        const pros: string[] = []
        const prosContent = prosMatch[1]

        // Look for {{Overview|Pros patterns and extract content
        const overviewProsMatch = prosContent.match(/\{\{Overview\|Pros[^}]*\|([\s\S]*?)\}\}/i)
        if (overviewProsMatch) {
          const prosLines = overviewProsMatch[1].split('\n').filter(line => line.trim().startsWith('*'))
          prosLines.forEach(line => {
            const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
            if (cleanLine.length > 10) {
              pros.push(cleanLine)
            }
          })
        } else {
          // Fallback: simple line extraction
          const prosLines = prosContent.split('\n').filter(line => line.trim().startsWith('*'))
          prosLines.forEach(line => {
            const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
            if (cleanLine.length > 10) {
              pros.push(cleanLine)
            }
          })
        }

        if (pros.length > 0) {
          weaponData.pros = pros.slice(0, 8) // Limit to 8 pros
        }
      }

      // Extract cons
      const consMatch = overviewContent.match(/Cons=([\s\S]*?)(?=\|-\||<\/tabber>|$)/i)
      if (consMatch) {
        const cons: string[] = []
        const consContent = consMatch[1]

        // Look for {{Overview|Cons patterns and extract content
        const overviewConsMatch = consContent.match(/\{\{Overview\|Cons[^}]*\|([\s\S]*?)\}\}/i)
        if (overviewConsMatch) {
          const consLines = overviewConsMatch[1].split('\n').filter(line => line.trim().startsWith('*'))
          consLines.forEach(line => {
            const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
            if (cleanLine.length > 10) {
              cons.push(cleanLine)
            }
          })
        } else {
          // Fallback: simple line extraction
          const consLines = consContent.split('\n').filter(line => line.trim().startsWith('*'))
          consLines.forEach(line => {
            const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
            if (cleanLine.length > 10) {
              cons.push(cleanLine)
            }
          })
        }

        if (cons.length > 0) {
          weaponData.cons = cons.slice(0, 8) // Limit to 8 cons
        }
      }
    }

    // Extract special abilities
    const specialAbilities: string[] = []

    // Look in common sections
    const abilityPatterns = [
      /==\s*Special\s*==([\s\S]*?)(?===|$)/i,
      /==\s*Abilities\s*==([\s\S]*?)(?===|$)/i,
      /==\s*Passive\s*==([\s\S]*?)(?===|$)/i
    ]

    abilityPatterns.forEach(pattern => {
      const match = wikitext.match(pattern)
      if (match) {
        const abilities = match[1]
          .split('\n')
          .map(line => this.cleanWikitext(line.trim()))
          .filter(line => line.length > 10 && !line.startsWith('{{') && !line.startsWith('=='))

        specialAbilities.push(...abilities)
      }
    })

    if (specialAbilities.length > 0) {
      weaponData.specialAbilities = [...new Set(specialAbilities)] // Remove duplicates
    }

    return weaponData
  }

  async scrapeItem(title: string, itemType: string): Promise<ScrapedItem | null> {
    console.log(`⚔️ Scraping weapon: ${title}`)

    const wikitext = await this.getPageContent(title)
    if (!wikitext) {
      console.warn(`⚠️ No content found for ${title}`)
      return null
    }

    // Extract infobox data
    const infoboxData = this.extractTemplateData(wikitext, "Infobox")

    // Extract weapon-specific data
    const weaponData = this.extractSpecificData(wikitext, infoboxData)

    // Create the scraped item
    const item: ScrapedItem = {
      name: title,
      type: itemType,
      category: weaponData.weaponType?.toLowerCase() || itemType,
      wikiUrl: `https://blox-fruits.fandom.com/wiki/${encodeURIComponent(title)}`,
      lastUpdated: new Date(),
      weaponData,
      rawData: {
        infobox: infoboxData,
        wikitextLength: wikitext.length,
        movesFound: 0,
        statsFound: weaponData.stats?.length || 0,
        extractedAt: new Date().toISOString(),
        weaponData
      }
    }

    // Extract basic properties from infobox
    if (infoboxData.rarity) item.rarity = infoboxData.rarity
    if (infoboxData.price) item.price = infoboxData.price
    if (infoboxData.description) item.description = this.cleanWikitext(infoboxData.description)
    if (infoboxData.location) item.location = infoboxData.location

    return item
  }

  async scrapeCategory(categoryName: string, weaponType: "sword" | "gun" = "sword"): Promise<ScrapedItem[]> {
    console.log(`\n🎯 Starting to scrape ${weaponType}s from category: ${categoryName}`)

    const members = await this.getCategoryMembers(categoryName)
    if (members.length === 0) {
      return []
    }

    const items: ScrapedItem[] = []
    let processed = 0

    for (const member of members) {
      try {
        const item = await this.scrapeItem(member.title, weaponType)
        if (item) {
          items.push(item)
        }
        processed++

        // Progress indicator
        if (processed % 5 === 0 || processed === members.length) {
          console.log(
            `📊 Progress: ${processed}/${members.length} (${Math.round((processed / members.length) * 100)}%)`
          )
        }

        // Small delay between items
        await new Promise((resolve) => setTimeout(resolve, 200))
      } catch (error) {
        console.error(`❌ Error scraping ${weaponType} ${member.title}:`, error)
      }
    }

    console.log(`✅ Completed ${weaponType}s: ${items.length}/${members.length} items scraped`)
    return items
  }
}
