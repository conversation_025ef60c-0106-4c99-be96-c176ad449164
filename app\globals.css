@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 220 13% 4%;
  --foreground: 210 40% 98%;
  --card: 220 13% 10%;
  --card-foreground: 210 40% 98%;
  --popover: 220 13% 10%;
  --popover-foreground: 210 40% 98%;
  --primary: 217 91% 60%;
  --primary-foreground: 222 84% 5%;
  --secondary: 217 32% 17%;
  --secondary-foreground: 210 40% 98%;
  --muted: 215 32% 17%;
  --muted-foreground: 217 10% 64%;
  --accent: 217 32% 17%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 210 40% 98%;
  --border: 217 32% 17%;
  --input: 217 32% 17%;
  --ring: 217 91% 60%;
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    background-image: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.05) 0%, transparent 50%);
  }
}

@layer components {
  .glow-effect {
    @apply relative;
  }

  .glow-effect::before {
    content: "";
    @apply absolute inset-0 rounded-lg opacity-0 transition-opacity duration-300;
    background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4);
    filter: blur(8px);
    z-index: -1;
  }

  .glow-effect:hover::before {
    @apply opacity-20;
  }

  .gaming-card {
    @apply bg-card/50 backdrop-blur-sm border border-border/50 rounded-lg;
    background-image: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  }

  .neon-text {
    text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 40px currentColor;
  }

  .stat-bar {
    @apply relative overflow-hidden rounded-full bg-secondary;
  }

  .stat-bar-fill {
    @apply h-full rounded-full transition-all duration-1000 ease-out;
    background: linear-gradient(90deg, #00ff88, #00ccff);
    box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
  }
}

.font-gaming {
  font-family: var(--font-orbitron);
}

.font-mono {
  font-family: var(--font-fira-code);
}
