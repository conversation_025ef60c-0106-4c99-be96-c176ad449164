import Link from "next/link"
import { Github, Twitter, DiscIcon as Discord } from "lucide-react"

export function Footer() {
  return (
    <footer className="border-t border-border/40 bg-background/95 backdrop-blur">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="space-y-3">
            <h3 className="font-gaming text-lg font-semibold">Blox Fruits Calculator</h3>
            <p className="text-sm text-muted-foreground">
              The ultimate calculator suite for Blox Fruits players. Optimize your gameplay with precise calculations.
            </p>
          </div>

          <div className="space-y-3">
            <h4 className="font-semibold">Calculators</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/calculator/stats" className="text-muted-foreground hover:text-foreground">
                  Stats Calculator
                </Link>
              </li>
              <li>
                <Link href="/calculator/damage" className="text-muted-foreground hover:text-foreground">
                  Damage Calculator
                </Link>
              </li>
              <li>
                <Link href="/calculator/farming" className="text-muted-foreground hover:text-foreground">
                  Farming Calculator
                </Link>
              </li>
              <li>
                <Link href="/calculator/trading" className="text-muted-foreground hover:text-foreground">
                  Trading Calculator
                </Link>
              </li>
            </ul>
          </div>

          <div className="space-y-3">
            <h4 className="font-semibold">Trading</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/trading" className="text-muted-foreground hover:text-foreground">
                  Trading Values
                </Link>
              </li>
            </ul>
          </div>

          <div className="space-y-3">
            <h4 className="font-semibold">Community</h4>
            <div className="flex space-x-4">
              <Link href="#" className="text-muted-foreground hover:text-foreground">
                <Discord className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t border-border/40 text-center text-sm text-muted-foreground">
          <p>&copy; 2025 Blox Fruits Calculator. Not affiliated with Roblox Corporation.</p>
        </div>
      </div>
    </footer>
  )
}
