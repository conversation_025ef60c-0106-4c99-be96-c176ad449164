"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { TrendingUp, TrendingDown, Minus, Search, Filter, RefreshCw, Star } from "lucide-react"
import { tradingValuesData, type TradingValue } from "@/lib/data"

const slugify = (text: string) =>
  text
    .toString()
    .toLowerCase()
    .replace(/\s+/g, "-")
    .replace(/[^\w-]+/g, "")
    .replace(/--+/g, "-")
    .replace(/^-+/, "")
    .replace(/-+$/, "")

export default function TradingValuesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState<string>("all")
  const [selectedRarity, setSelectedRarity] = useState<string>("all")
  const [sortBy, setSortBy] = useState<string>("value")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [favorites, setFavorites] = useState<string[]>([])
  const [lastUpdate, setLastUpdate] = useState(new Date())

  const tradingValues = tradingValuesData

  const filteredAndSortedValues = tradingValues
    .filter((item) => {
      const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesType = selectedType === "all" || item.type === selectedType
      const matchesRarity = selectedRarity === "all" || item.rarity === selectedRarity
      return matchesSearch && matchesType && matchesRarity
    })
    .sort((a, b) => {
      let aValue: number, bValue: number

      switch (sortBy) {
        case "value":
          aValue = a.currentValue
          bValue = b.currentValue
          break
        case "change":
          aValue = a.changePercent
          bValue = b.changePercent
          break
        case "volume":
          aValue = a.volume
          bValue = b.volume
          break
        case "confidence":
          aValue = a.confidence
          bValue = b.confidence
          break
        default:
          aValue = a.currentValue
          bValue = b.currentValue
      }

      return sortOrder === "desc" ? bValue - aValue : aValue - bValue
    })

  const getTrendIcon = (trend: string) => {
    if (trend === "up") return <TrendingUp className="w-4 h-4 text-green-400" />
    if (trend === "down") return <TrendingDown className="w-4 h-4 text-red-400" />
    return <Minus className="w-4 h-4 text-gray-400" />
  }

  const getTrendColor = (trend: string) => {
    if (trend === "up") return "text-green-400"
    if (trend === "down") return "text-red-400"
    return "text-gray-400"
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case "Common":
        return "bg-gray-500/20 text-gray-400"
      case "Uncommon":
        return "bg-green-500/20 text-green-400"
      case "Rare":
        return "bg-blue-500/20 text-blue-400"
      case "Legendary":
        return "bg-purple-500/20 text-purple-400"
      case "Mythical":
        return "bg-yellow-500/20 text-yellow-400"
      default:
        return "bg-gray-500/20 text-gray-400"
    }
  }

  const toggleFavorite = (e: React.MouseEvent, itemName: string) => {
    e.preventDefault()
    e.stopPropagation()
    setFavorites((prev) => (prev.includes(itemName) ? prev.filter((name) => name !== itemName) : [...prev, itemName]))
  }

  const refreshData = () => {
    setLastUpdate(new Date())
  }

  const renderItemCard = (item: TradingValue) => (
    <Link href={`/trading/${slugify(item.name)}`} key={item.name} className="block group">
      <Card className="gaming-card h-full group-hover:border-primary transition-colors">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => toggleFavorite(e, item.name)}
                className={favorites.includes(item.name) ? "text-yellow-400" : "text-gray-400"}
              >
                <Star className={`w-4 h-4 ${favorites.includes(item.name) ? "fill-current" : ""}`} />
              </Button>
              <div>
                <h3 className="font-semibold text-lg">{item.name}</h3>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs capitalize">
                    {item.type}
                  </Badge>
                  <Badge className={`text-xs ${getRarityColor(item.rarity)}`}>{item.rarity}</Badge>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-6">
              <div className="text-right">
                <div className="font-mono text-2xl font-bold text-primary">{item.currentValue.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Current Value</div>
              </div>

              <div className="text-right">
                <div className={`flex items-center justify-end gap-1 font-semibold ${getTrendColor(item.trend)}`}>
                  {getTrendIcon(item.trend)}
                  <span>
                    {item.changePercent > 0 ? "+" : ""}
                    {item.changePercent.toFixed(1)}%
                  </span>
                </div>
                <div className="text-sm text-muted-foreground">24h Change</div>
              </div>

              <div className="text-right hidden md:block">
                <div className="font-mono font-semibold">{item.volume.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Volume</div>
              </div>

              <div className="text-right hidden lg:block">
                <div
                  className={`font-semibold ${
                    item.confidence >= 90
                      ? "text-green-400"
                      : item.confidence >= 80
                        ? "text-yellow-400"
                        : "text-red-400"
                  }`}
                >
                  {item.confidence}%
                </div>
                <div className="text-sm text-muted-foreground">Confidence</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="font-gaming text-3xl font-bold mb-2 flex items-center gap-2">
              <TrendingUp className="w-8 h-8 text-primary" />
              Trading Values
            </h1>
            <p className="text-muted-foreground">Live market prices and trading data</p>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Last updated: {lastUpdate.toLocaleTimeString()}</span>
            <Button onClick={refreshData} size="sm" variant="outline">
              <RefreshCw className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      <Card className="gaming-card mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger>
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="fruit">Fruits</SelectItem>
                <SelectItem value="sword">Swords</SelectItem>
                <SelectItem value="accessory">Accessories</SelectItem>
                <SelectItem value="gun">Guns</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedRarity} onValueChange={setSelectedRarity}>
              <SelectTrigger>
                <SelectValue placeholder="Rarity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Rarities</SelectItem>
                <SelectItem value="Mythical">Mythical</SelectItem>
                <SelectItem value="Legendary">Legendary</SelectItem>
                <SelectItem value="Rare">Rare</SelectItem>
                <SelectItem value="Uncommon">Uncommon</SelectItem>
                <SelectItem value="Common">Common</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="value">Value</SelectItem>
                <SelectItem value="change">Change %</SelectItem>
                <SelectItem value="volume">Volume</SelectItem>
                <SelectItem value="confidence">Confidence</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortOrder} onValueChange={(value: "asc" | "desc") => setSortOrder(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">High to Low</SelectItem>
                <SelectItem value="asc">Low to High</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="all" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4">
          <TabsTrigger value="all">All Items</TabsTrigger>
          <TabsTrigger value="gainers">Top Gainers</TabsTrigger>
          <TabsTrigger value="losers">Top Losers</TabsTrigger>
          <TabsTrigger value="favorites">Favorites</TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <div className="grid grid-cols-1 gap-4">{filteredAndSortedValues.map(renderItemCard)}</div>
        </TabsContent>

        <TabsContent value="gainers">
          <div className="grid grid-cols-1 gap-4">
            {filteredAndSortedValues
              .filter((item) => item.trend === "up")
              .slice(0, 10)
              .map(renderItemCard)}
          </div>
        </TabsContent>

        <TabsContent value="losers">
          <div className="grid grid-cols-1 gap-4">
            {filteredAndSortedValues
              .filter((item) => item.trend === "down")
              .sort((a, b) => a.changePercent - b.changePercent)
              .slice(0, 10)
              .map(renderItemCard)}
          </div>
        </TabsContent>

        <TabsContent value="favorites">
          <div className="grid grid-cols-1 gap-4">
            {favorites.length === 0 ? (
              <Card className="gaming-card">
                <CardContent className="text-center py-12">
                  <Star className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-semibold mb-2">No favorites yet</h3>
                  <p className="text-muted-foreground">Click the star icon on items to add them to your favorites</p>
                </CardContent>
              </Card>
            ) : (
              filteredAndSortedValues.filter((item) => favorites.includes(item.name)).map(renderItemCard)
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
