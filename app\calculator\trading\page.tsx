"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import {
  TrendingUp,
  TrendingDown,
  Minus,
  AlertTriangle,
  CheckCircle,
  Copy,
  RefreshCw,
  Search,
  Star,
  Clock,
  BarChart3,
} from "lucide-react"
import { toast } from "sonner"

interface TradingItem {
  name: string
  type: "fruit" | "sword" | "accessory" | "gun" | "material"
  currentValue: number
  trend: "up" | "down" | "stable"
  confidence: number
  lastUpdated: Date
  rarity: "common" | "uncommon" | "rare" | "legendary" | "mythical"
  demand: "low" | "medium" | "high" | "very_high"
  volatility: number
}

export default function TradingCalculatorPage() {
  const [offerItems, setOfferItems] = useState<string[]>([])
  const [wantItems, setWantItems] = useState<string[]>([])
  const [selectedOfferItem, setSelectedOfferItem] = useState("")
  const [selectedWantItem, setSelectedWantItem] = useState("")
  const [tradeAnalysis, setTradeAnalysis] = useState<any>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState<string>("all")
  const [filterRarity, setFilterRarity] = useState<string>("all")
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [autoUpdate, setAutoUpdate] = useState(true)

  // Base de données de trading complète et réaliste
  const tradingData: TradingItem[] = [
    // Fruits Mythiques
    {
      name: "Leopard",
      type: "fruit",
      currentValue: 5000000,
      trend: "stable",
      confidence: 95,
      lastUpdated: new Date(),
      rarity: "mythical",
      demand: "very_high",
      volatility: 15,
    },
    {
      name: "Dragon",
      type: "fruit",
      currentValue: 3500000,
      trend: "up",
      confidence: 92,
      lastUpdated: new Date(),
      rarity: "mythical",
      demand: "very_high",
      volatility: 12,
    },
    {
      name: "Kitsune",
      type: "fruit",
      currentValue: 8000000,
      trend: "up",
      confidence: 88,
      lastUpdated: new Date(),
      rarity: "mythical",
      demand: "very_high",
      volatility: 25,
    },
    {
      name: "T-Rex",
      type: "fruit",
      currentValue: 2700000,
      trend: "stable",
      confidence: 90,
      lastUpdated: new Date(),
      rarity: "mythical",
      demand: "high",
      volatility: 18,
    },

    // Fruits Légendaires
    {
      name: "Spirit",
      type: "fruit",
      currentValue: 3400000,
      trend: "down",
      confidence: 85,
      lastUpdated: new Date(),
      rarity: "legendary",
      demand: "high",
      volatility: 20,
    },
    {
      name: "Control",
      type: "fruit",
      currentValue: 3200000,
      trend: "up",
      confidence: 88,
      lastUpdated: new Date(),
      rarity: "legendary",
      demand: "high",
      volatility: 16,
    },
    {
      name: "Venom",
      type: "fruit",
      currentValue: 3000000,
      trend: "stable",
      confidence: 92,
      lastUpdated: new Date(),
      rarity: "legendary",
      demand: "very_high",
      volatility: 14,
    },
    {
      name: "Dough",
      type: "fruit",
      currentValue: 2800000,
      trend: "up",
      confidence: 87,
      lastUpdated: new Date(),
      rarity: "legendary",
      demand: "very_high",
      volatility: 22,
    },
    {
      name: "Shadow",
      type: "fruit",
      currentValue: 2900000,
      trend: "down",
      confidence: 83,
      lastUpdated: new Date(),
      rarity: "legendary",
      demand: "high",
      volatility: 19,
    },
    {
      name: "Blizzard",
      type: "fruit",
      currentValue: 2400000,
      trend: "stable",
      confidence: 86,
      lastUpdated: new Date(),
      rarity: "legendary",
      demand: "medium",
      volatility: 17,
    },
    {
      name: "Mammoth",
      type: "fruit",
      currentValue: 2700000,
      trend: "up",
      confidence: 84,
      lastUpdated: new Date(),
      rarity: "legendary",
      demand: "high",
      volatility: 21,
    },

    // Fruits Rares
    {
      name: "Buddha",
      type: "fruit",
      currentValue: 1200000,
      trend: "stable",
      confidence: 94,
      lastUpdated: new Date(),
      rarity: "rare",
      demand: "very_high",
      volatility: 8,
    },
    {
      name: "Phoenix",
      type: "fruit",
      currentValue: 1800000,
      trend: "up",
      confidence: 89,
      lastUpdated: new Date(),
      rarity: "rare",
      demand: "high",
      volatility: 13,
    },
    {
      name: "Portal",
      type: "fruit",
      currentValue: 1900000,
      trend: "stable",
      confidence: 91,
      lastUpdated: new Date(),
      rarity: "rare",
      demand: "high",
      volatility: 15,
    },
    {
      name: "Pain",
      type: "fruit",
      currentValue: 2300000,
      trend: "down",
      confidence: 82,
      lastUpdated: new Date(),
      rarity: "rare",
      demand: "medium",
      volatility: 24,
    },

    // Épées Légendaires
    {
      name: "Cursed Dual Katana",
      type: "sword",
      currentValue: 650000,
      trend: "up",
      confidence: 78,
      lastUpdated: new Date(),
      rarity: "legendary",
      demand: "very_high",
      volatility: 30,
    },
    {
      name: "Dark Blade",
      type: "sword",
      currentValue: 1000000,
      trend: "stable",
      confidence: 82,
      lastUpdated: new Date(),
      rarity: "legendary",
      demand: "high",
      volatility: 25,
    },
    {
      name: "True Triple Katana",
      type: "sword",
      currentValue: 2000000,
      trend: "up",
      confidence: 85,
      lastUpdated: new Date(),
      rarity: "mythical",
      demand: "very_high",
      volatility: 35,
    },
    {
      name: "Yoru",
      type: "sword",
      currentValue: 1200000,
      trend: "stable",
      confidence: 88,
      lastUpdated: new Date(),
      rarity: "legendary",
      demand: "high",
      volatility: 20,
    },

    // Accessoires
    {
      name: "Pale Scarf",
      type: "accessory",
      currentValue: 300000,
      trend: "up",
      confidence: 75,
      lastUpdated: new Date(),
      rarity: "rare",
      demand: "medium",
      volatility: 40,
    },
    {
      name: "Valkyrie Helmet",
      type: "accessory",
      currentValue: 100000,
      trend: "stable",
      confidence: 80,
      lastUpdated: new Date(),
      rarity: "uncommon",
      demand: "medium",
      volatility: 15,
    },

    // Matériaux
    {
      name: "Dragon Scale",
      type: "material",
      currentValue: 3000,
      trend: "stable",
      confidence: 95,
      lastUpdated: new Date(),
      rarity: "rare",
      demand: "high",
      volatility: 10,
    },
    {
      name: "Mystic Droplet",
      type: "material",
      currentValue: 1000,
      trend: "up",
      confidence: 90,
      lastUpdated: new Date(),
      rarity: "uncommon",
      demand: "medium",
      volatility: 20,
    },
  ]

  const addOfferItem = () => {
    if (selectedOfferItem && !offerItems.includes(selectedOfferItem)) {
      setOfferItems([...offerItems, selectedOfferItem])
      setSelectedOfferItem("")
      toast.success(`${selectedOfferItem} ajouté à votre offre`)
    }
  }

  const addWantItem = () => {
    if (selectedWantItem && !wantItems.includes(selectedWantItem)) {
      setWantItems([...wantItems, selectedWantItem])
      setSelectedWantItem("")
      toast.success(`${selectedWantItem} ajouté à vos demandes`)
    }
  }

  const removeOfferItem = (item: string) => {
    setOfferItems(offerItems.filter((i) => i !== item))
    toast.info(`${item} retiré de votre offre`)
  }

  const removeWantItem = (item: string) => {
    setWantItems(wantItems.filter((i) => i !== item))
    toast.info(`${item} retiré de vos demandes`)
  }

  const analyzeTrade = () => {
    const offerValue = offerItems.reduce((total, itemName) => {
      const item = tradingData.find((i) => i.name === itemName)
      return total + (item?.currentValue || 0)
    }, 0)

    const wantValue = wantItems.reduce((total, itemName) => {
      const item = tradingData.find((i) => i.name === itemName)
      return total + (item?.currentValue || 0)
    }, 0)

    const difference = offerValue - wantValue
    const percentageDiff = wantValue > 0 ? (difference / wantValue) * 100 : 0

    // Calcul de la volatilité moyenne
    const offerVolatility =
      offerItems.reduce((total, itemName) => {
        const item = tradingData.find((i) => i.name === itemName)
        return total + (item?.volatility || 0)
      }, 0) / (offerItems.length || 1)

    const wantVolatility =
      wantItems.reduce((total, itemName) => {
        const item = tradingData.find((i) => i.name === itemName)
        return total + (item?.volatility || 0)
      }, 0) / (wantItems.length || 1)

    let tradeStatus: "fair" | "good" | "bad" | "scam" | "excellent"
    let statusColor: string
    let statusIcon: any
    let recommendation: string

    if (Math.abs(percentageDiff) <= 5) {
      tradeStatus = "excellent"
      statusColor = "text-purple-400"
      statusIcon = Star
      recommendation = "Trade parfait ! Valeurs quasi-identiques."
    } else if (Math.abs(percentageDiff) <= 15) {
      tradeStatus = "fair"
      statusColor = "text-green-400"
      statusIcon = CheckCircle
      recommendation = "Trade équitable, bonne affaire."
    } else if (percentageDiff > 15) {
      tradeStatus = "good"
      statusColor = "text-blue-400"
      statusIcon = TrendingUp
      recommendation = "Vous êtes avantagé dans ce trade !"
    } else if (percentageDiff > -30) {
      tradeStatus = "bad"
      statusColor = "text-yellow-400"
      statusIcon = TrendingDown
      recommendation = "Vous pourriez perdre de la valeur."
    } else {
      tradeStatus = "scam"
      statusColor = "text-red-400"
      statusIcon = AlertTriangle
      recommendation = "⚠️ ATTENTION : Potentiel scam !"
    }

    setTradeAnalysis({
      offerValue,
      wantValue,
      difference,
      percentageDiff,
      tradeStatus,
      statusColor,
      statusIcon,
      recommendation,
      offerVolatility,
      wantVolatility,
      riskLevel: Math.max(offerVolatility, wantVolatility),
    })
  }

  const resetTrade = () => {
    setOfferItems([])
    setWantItems([])
    setTradeAnalysis(null)
    toast.info("Trade réinitialisé")
  }

  const copyTradeResults = () => {
    if (!tradeAnalysis) return

    const results = `
🔄 ANALYSE DE TRADE BLOX FRUITS

💰 Votre offre: ${tradeAnalysis.offerValue.toLocaleString()}
💎 Leur offre: ${tradeAnalysis.wantValue.toLocaleString()}
📊 Différence: ${tradeAnalysis.difference >= 0 ? "+" : ""}${tradeAnalysis.difference.toLocaleString()}
📈 Pourcentage: ${tradeAnalysis.percentageDiff >= 0 ? "+" : ""}${tradeAnalysis.percentageDiff.toFixed(1)}%

✅ Statut: ${tradeAnalysis.tradeStatus.toUpperCase()}
💡 ${tradeAnalysis.recommendation}

🎯 Généré par Blox Fruits Calculator
    `.trim()

    navigator.clipboard.writeText(results)
    toast.success("Résultats copiés dans le presse-papier !")
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="w-4 h-4 text-green-400" />
      case "down":
        return <TrendingDown className="w-4 h-4 text-red-400" />
      default:
        return <Minus className="w-4 h-4 text-gray-400" />
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return "text-green-400"
    if (confidence >= 80) return "text-yellow-400"
    return "text-red-400"
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case "mythical":
        return "text-purple-400 border-purple-400/50"
      case "legendary":
        return "text-orange-400 border-orange-400/50"
      case "rare":
        return "text-blue-400 border-blue-400/50"
      case "uncommon":
        return "text-green-400 border-green-400/50"
      default:
        return "text-gray-400 border-gray-400/50"
    }
  }

  const getDemandIcon = (demand: string) => {
    switch (demand) {
      case "very_high":
        return "🔥"
      case "high":
        return "📈"
      case "medium":
        return "📊"
      default:
        return "📉"
    }
  }

  const filteredItems = tradingData.filter((item) => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === "all" || item.type === filterType
    const matchesRarity = filterRarity === "all" || item.rarity === filterRarity
    return matchesSearch && matchesType && matchesRarity
  })

  useEffect(() => {
    if (offerItems.length > 0 && wantItems.length > 0 && autoUpdate) {
      analyzeTrade()
    } else {
      setTradeAnalysis(null)
    }
  }, [offerItems, wantItems, autoUpdate])

  // Quick Stats
  const totalOfferValue = offerItems.reduce((total, itemName) => {
    const item = tradingData.find((i) => i.name === itemName)
    return total + (item?.currentValue || 0)
  }, 0)

  const totalWantValue = wantItems.reduce((total, itemName) => {
    const item = tradingData.find((i) => i.name === itemName)
    return total + (item?.currentValue || 0)
  }, 0)

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="font-gaming text-3xl font-bold mb-2 flex items-center gap-2">
          <TrendingUp className="w-8 h-8 text-primary" />
          Trading Calculator
        </h1>
        <p className="text-muted-foreground">Analysez vos trades et obtenez des recommandations équitables</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <Card className="gaming-card">
          <CardContent className="pt-4">
            <div className="text-2xl font-bold text-green-400">{totalOfferValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Valeur Offre</p>
          </CardContent>
        </Card>
        <Card className="gaming-card">
          <CardContent className="pt-4">
            <div className="text-2xl font-bold text-blue-400">{totalWantValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Valeur Demande</p>
          </CardContent>
        </Card>
        <Card className="gaming-card">
          <CardContent className="pt-4">
            <div className="text-2xl font-bold text-primary">{offerItems.length + wantItems.length}</div>
            <p className="text-xs text-muted-foreground">Items Total</p>
          </CardContent>
        </Card>
        <Card className="gaming-card">
          <CardContent className="pt-4">
            <div className="text-2xl font-bold text-purple-400">
              {tradeAnalysis ? tradeAnalysis.tradeStatus.toUpperCase() : "N/A"}
            </div>
            <p className="text-xs text-muted-foreground">Statut Trade</p>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card className="gaming-card mb-6">
        <CardContent className="pt-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center space-x-2">
              <Switch id="auto-update" checked={autoUpdate} onCheckedChange={setAutoUpdate} />
              <Label htmlFor="auto-update">Auto-analyse</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch id="advanced" checked={showAdvanced} onCheckedChange={setShowAdvanced} />
              <Label htmlFor="advanced">Mode avancé</Label>
            </div>
            <Button onClick={resetTrade} variant="outline" size="sm">
              <RefreshCw className="w-4 h-4 mr-2" />
              Reset
            </Button>
            {tradeAnalysis && (
              <Button onClick={copyTradeResults} variant="outline" size="sm">
                <Copy className="w-4 h-4 mr-2" />
                Copier résultats
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="calculator" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="calculator">Trade Calculator</TabsTrigger>
          <TabsTrigger value="values">Valeurs Actuelles</TabsTrigger>
          <TabsTrigger value="trends">Tendances Marché</TabsTrigger>
          <TabsTrigger value="analysis">Analyse Avancée</TabsTrigger>
        </TabsList>

        <TabsContent value="calculator" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Offer Section */}
            <Card className="gaming-card">
              <CardHeader>
                <CardTitle className="text-green-400 flex items-center gap-2">
                  <TrendingUp className="w-5 h-5" />
                  Votre Offre
                </CardTitle>
                <CardDescription>Ajoutez les items que vous proposez</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Select value={selectedOfferItem} onValueChange={setSelectedOfferItem}>
                    <SelectTrigger className="flex-1">
                      <SelectValue placeholder="Sélectionner un item à offrir" />
                    </SelectTrigger>
                    <SelectContent>
                      {filteredItems
                        .filter((item) => !offerItems.includes(item.name))
                        .map((item) => (
                          <SelectItem key={item.name} value={item.name}>
                            <div className="flex items-center gap-2">
                              <span>{getDemandIcon(item.demand)}</span>
                              <span>{item.name}</span>
                              <span className="text-xs text-muted-foreground">
                                {item.currentValue.toLocaleString()}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                  <Button onClick={addOfferItem} disabled={!selectedOfferItem}>
                    Ajouter
                  </Button>
                </div>

                <div className="space-y-2">
                  {offerItems.map((itemName) => {
                    const item = tradingData.find((i) => i.name === itemName)
                    return (
                      <div
                        key={itemName}
                        className="flex items-center justify-between p-3 bg-green-500/10 rounded-lg border border-green-500/20"
                      >
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-semibold">{itemName}</span>
                            <Badge variant="outline" className={getRarityColor(item?.rarity || "common")}>
                              {item?.rarity}
                            </Badge>
                            {getTrendIcon(item?.trend || "stable")}
                          </div>
                          <div className="text-sm text-muted-foreground font-mono">
                            {item?.currentValue.toLocaleString()}
                            {showAdvanced && (
                              <span className="ml-2 text-xs">
                                Vol: {item?.volatility}% | {getDemandIcon(item?.demand || "medium")}
                              </span>
                            )}
                          </div>
                        </div>
                        <Button variant="ghost" size="sm" onClick={() => removeOfferItem(itemName)}>
                          ×
                        </Button>
                      </div>
                    )
                  })}
                </div>

                {offerItems.length > 0 && (
                  <div className="pt-3 border-t border-border/50">
                    <div className="flex justify-between font-semibold">
                      <span>Valeur Totale:</span>
                      <span className="font-mono text-green-400">{totalOfferValue.toLocaleString()}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Want Section */}
            <Card className="gaming-card">
              <CardHeader>
                <CardTitle className="text-blue-400 flex items-center gap-2">
                  <TrendingDown className="w-5 h-5" />
                  Vos Demandes
                </CardTitle>
                <CardDescription>Ajoutez les items que vous voulez recevoir</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Select value={selectedWantItem} onValueChange={setSelectedWantItem}>
                    <SelectTrigger className="flex-1">
                      <SelectValue placeholder="Sélectionner un item voulu" />
                    </SelectTrigger>
                    <SelectContent>
                      {filteredItems
                        .filter((item) => !wantItems.includes(item.name))
                        .map((item) => (
                          <SelectItem key={item.name} value={item.name}>
                            <div className="flex items-center gap-2">
                              <span>{getDemandIcon(item.demand)}</span>
                              <span>{item.name}</span>
                              <span className="text-xs text-muted-foreground">
                                {item.currentValue.toLocaleString()}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                  <Button onClick={addWantItem} disabled={!selectedWantItem}>
                    Ajouter
                  </Button>
                </div>

                <div className="space-y-2">
                  {wantItems.map((itemName) => {
                    const item = tradingData.find((i) => i.name === itemName)
                    return (
                      <div
                        key={itemName}
                        className="flex items-center justify-between p-3 bg-blue-500/10 rounded-lg border border-blue-500/20"
                      >
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-semibold">{itemName}</span>
                            <Badge variant="outline" className={getRarityColor(item?.rarity || "common")}>
                              {item?.rarity}
                            </Badge>
                            {getTrendIcon(item?.trend || "stable")}
                          </div>
                          <div className="text-sm text-muted-foreground font-mono">
                            {item?.currentValue.toLocaleString()}
                            {showAdvanced && (
                              <span className="ml-2 text-xs">
                                Vol: {item?.volatility}% | {getDemandIcon(item?.demand || "medium")}
                              </span>
                            )}
                          </div>
                        </div>
                        <Button variant="ghost" size="sm" onClick={() => removeWantItem(itemName)}>
                          ×
                        </Button>
                      </div>
                    )
                  })}
                </div>

                {wantItems.length > 0 && (
                  <div className="pt-3 border-t border-border/50">
                    <div className="flex justify-between font-semibold">
                      <span>Valeur Totale:</span>
                      <span className="font-mono text-blue-400">{totalWantValue.toLocaleString()}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Analysis Section */}
            <Card className="gaming-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Analyse du Trade
                </CardTitle>
                <CardDescription>Évaluation équitable du trade</CardDescription>
              </CardHeader>
              <CardContent>
                {tradeAnalysis ? (
                  <div className="space-y-4">
                    <div className="text-center">
                      <div
                        className={`flex items-center justify-center gap-2 text-2xl font-bold ${tradeAnalysis.statusColor}`}
                      >
                        <tradeAnalysis.statusIcon className="w-8 h-8" />
                        {tradeAnalysis.tradeStatus.toUpperCase()}
                      </div>
                      <div className="text-sm text-muted-foreground mt-1">Statut du Trade</div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span>Votre Offre:</span>
                        <span className="font-mono text-green-400">{tradeAnalysis.offerValue.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Leur Offre:</span>
                        <span className="font-mono text-blue-400">{tradeAnalysis.wantValue.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between font-semibold">
                        <span>Différence:</span>
                        <span
                          className={`font-mono ${tradeAnalysis.difference >= 0 ? "text-green-400" : "text-red-400"}`}
                        >
                          {tradeAnalysis.difference >= 0 ? "+" : ""}
                          {tradeAnalysis.difference.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Pourcentage:</span>
                        <span
                          className={`font-mono ${tradeAnalysis.percentageDiff >= 0 ? "text-green-400" : "text-red-400"}`}
                        >
                          {tradeAnalysis.percentageDiff >= 0 ? "+" : ""}
                          {tradeAnalysis.percentageDiff.toFixed(1)}%
                        </span>
                      </div>

                      {showAdvanced && (
                        <>
                          <div className="flex justify-between">
                            <span>Risque:</span>
                            <span
                              className={`font-mono ${tradeAnalysis.riskLevel > 25 ? "text-red-400" : tradeAnalysis.riskLevel > 15 ? "text-yellow-400" : "text-green-400"}`}
                            >
                              {tradeAnalysis.riskLevel.toFixed(0)}%
                            </span>
                          </div>
                        </>
                      )}
                    </div>

                    <div className="pt-3 border-t border-border/50">
                      <div className="text-sm text-center">
                        <div className="font-semibold mb-1">💡 Recommandation</div>
                        <div className="text-muted-foreground">{tradeAnalysis.recommendation}</div>
                      </div>
                    </div>

                    {!autoUpdate && (
                      <Button onClick={analyzeTrade} className="w-full">
                        <BarChart3 className="w-4 h-4 mr-2" />
                        Analyser le Trade
                      </Button>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <TrendingUp className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>Ajoutez des items des deux côtés pour analyser le trade</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="values" className="space-y-6">
          <Card className="gaming-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                Valeurs de Trading Actuelles
              </CardTitle>
              <CardDescription>Prix du marché mis à jour en temps réel</CardDescription>
            </CardHeader>
            <CardContent>
              {/* Filtres */}
              <div className="flex flex-wrap gap-4 mb-6">
                <div className="flex items-center gap-2">
                  <Search className="w-4 h-4" />
                  <Input
                    placeholder="Rechercher un item..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-48"
                  />
                </div>
                <Select value={filterType} onValueChange={setFilterType}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous types</SelectItem>
                    <SelectItem value="fruit">Fruits</SelectItem>
                    <SelectItem value="sword">Épées</SelectItem>
                    <SelectItem value="accessory">Accessoires</SelectItem>
                    <SelectItem value="material">Matériaux</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={filterRarity} onValueChange={setFilterRarity}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes raretés</SelectItem>
                    <SelectItem value="mythical">Mythique</SelectItem>
                    <SelectItem value="legendary">Légendaire</SelectItem>
                    <SelectItem value="rare">Rare</SelectItem>
                    <SelectItem value="uncommon">Peu commun</SelectItem>
                    <SelectItem value="common">Commun</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredItems.map((item) => (
                  <Card key={item.name} className="bg-secondary/20 border-border/50">
                    <CardContent className="pt-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <h3 className="font-semibold">{item.name}</h3>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {item.type}
                            </Badge>
                            <Badge variant="outline" className={`text-xs ${getRarityColor(item.rarity)}`}>
                              {item.rarity}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          {getTrendIcon(item.trend)}
                          <span className="text-xs">{getDemandIcon(item.demand)}</span>
                        </div>
                      </div>
                      <div className="font-mono text-lg font-bold text-primary mb-1">
                        {item.currentValue.toLocaleString()}
                      </div>
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span className={getConfidenceColor(item.confidence)}>{item.confidence}% confiance</span>
                        <span>{item.lastUpdated.toLocaleTimeString()}</span>
                      </div>
                      {showAdvanced && (
                        <div className="mt-2 text-xs text-muted-foreground">
                          <div>Volatilité: {item.volatility}%</div>
                          <div>Demande: {item.demand}</div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <Card className="gaming-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Tendances du Marché
              </CardTitle>
              <CardDescription>Mouvements de prix et analyse du marché</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="font-semibold text-green-400 mb-3 flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Valeurs en Hausse ({tradingData.filter((item) => item.trend === "up").length})
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {tradingData
                      .filter((item) => item.trend === "up")
                      .sort((a, b) => b.currentValue - a.currentValue)
                      .map((item) => (
                        <div
                          key={item.name}
                          className="flex justify-between items-center p-3 bg-green-500/10 rounded-lg border border-green-500/20"
                        >
                          <div>
                            <span className="font-semibold">{item.name}</span>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className={`text-xs ${getRarityColor(item.rarity)}`}>
                                {item.rarity}
                              </Badge>
                              <span className="text-xs">{getDemandIcon(item.demand)}</span>
                            </div>
                          </div>
                          <div className="text-right">
                            <span className="font-mono text-green-400">{item.currentValue.toLocaleString()}</span>
                            {showAdvanced && (
                              <div className="text-xs text-muted-foreground">Vol: {item.volatility}%</div>
                            )}
                          </div>
                        </div>
                      ))}
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-red-400 mb-3 flex items-center gap-2">
                    <TrendingDown className="w-5 h-5" />
                    Valeurs en Baisse ({tradingData.filter((item) => item.trend === "down").length})
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {tradingData
                      .filter((item) => item.trend === "down")
                      .sort((a, b) => b.currentValue - a.currentValue)
                      .map((item) => (
                        <div
                          key={item.name}
                          className="flex justify-between items-center p-3 bg-red-500/10 rounded-lg border border-red-500/20"
                        >
                          <div>
                            <span className="font-semibold">{item.name}</span>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className={`text-xs ${getRarityColor(item.rarity)}`}>
                                {item.rarity}
                              </Badge>
                              <span className="text-xs">{getDemandIcon(item.demand)}</span>
                            </div>
                          </div>
                          <div className="text-right">
                            <span className="font-mono text-red-400">{item.currentValue.toLocaleString()}</span>
                            {showAdvanced && (
                              <div className="text-xs text-muted-foreground">Vol: {item.volatility}%</div>
                            )}
                          </div>
                        </div>
                      ))}
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-400 mb-3 flex items-center gap-2">
                    <Minus className="w-5 h-5" />
                    Valeurs Stables ({tradingData.filter((item) => item.trend === "stable").length})
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {tradingData
                      .filter((item) => item.trend === "stable")
                      .sort((a, b) => b.currentValue - a.currentValue)
                      .map((item) => (
                        <div
                          key={item.name}
                          className="flex justify-between items-center p-3 bg-gray-500/10 rounded-lg border border-gray-500/20"
                        >
                          <div>
                            <span className="font-semibold">{item.name}</span>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className={`text-xs ${getRarityColor(item.rarity)}`}>
                                {item.rarity}
                              </Badge>
                              <span className="text-xs">{getDemandIcon(item.demand)}</span>
                            </div>
                          </div>
                          <div className="text-right">
                            <span className="font-mono text-gray-400">{item.currentValue.toLocaleString()}</span>
                            {showAdvanced && (
                              <div className="text-xs text-muted-foreground">Vol: {item.volatility}%</div>
                            )}
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="gaming-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="w-5 h-5 text-purple-400" />
                  Top Items par Valeur
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {tradingData
                    .sort((a, b) => b.currentValue - a.currentValue)
                    .slice(0, 10)
                    .map((item, index) => (
                      <div key={item.name} className="flex items-center gap-3 p-2 rounded-lg bg-secondary/20">
                        <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center text-xs font-bold">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold">{item.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {item.type} • {item.rarity}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-mono font-bold">{item.currentValue.toLocaleString()}</div>
                          <div className="flex items-center gap-1">
                            {getTrendIcon(item.trend)}
                            <span className="text-xs">{getDemandIcon(item.demand)}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>

            <Card className="gaming-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5 text-yellow-400" />
                  Items à Risque Élevé
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {tradingData
                    .sort((a, b) => b.volatility - a.volatility)
                    .slice(0, 10)
                    .map((item, index) => (
                      <div
                        key={item.name}
                        className="flex items-center gap-3 p-2 rounded-lg bg-yellow-500/10 border border-yellow-500/20"
                      >
                        <div className="w-6 h-6 rounded-full bg-yellow-500/20 flex items-center justify-center text-xs font-bold">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <div className="font-semibold">{item.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {item.type} • {item.rarity}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-mono font-bold text-yellow-400">{item.volatility}%</div>
                          <div className="text-xs text-muted-foreground">Volatilité</div>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="gaming-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Statistiques du Marché
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-secondary/20 rounded-lg">
                  <div className="text-2xl font-bold text-primary">{tradingData.length}</div>
                  <div className="text-sm text-muted-foreground">Items Total</div>
                </div>
                <div className="text-center p-4 bg-secondary/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-400">
                    {Math.round(
                      tradingData.reduce((sum, item) => sum + item.currentValue, 0) / tradingData.length,
                    ).toLocaleString()}
                  </div>
                  <div className="text-sm text-muted-foreground">Valeur Moyenne</div>
                </div>
                <div className="text-center p-4 bg-secondary/20 rounded-lg">
                  <div className="text-2xl font-bold text-blue-400">
                    {Math.round(tradingData.reduce((sum, item) => sum + item.volatility, 0) / tradingData.length)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Volatilité Moyenne</div>
                </div>
                <div className="text-center p-4 bg-secondary/20 rounded-lg">
                  <div className="text-2xl font-bold text-purple-400">
                    {Math.round(tradingData.reduce((sum, item) => sum + item.confidence, 0) / tradingData.length)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Confiance Moyenne</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
