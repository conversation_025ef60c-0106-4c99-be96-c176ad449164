import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"
import { MongoClient } from "mongodb"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function runScraper() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    console.log("or your MongoDB connection string")
    process.exit(1)
  }

  const scraper = new FandomAPIScraper(mongoUrl)
  const client = new MongoClient(mongoUrl)

  try {
    await client.connect()
    const db = client.db("bloxfruits")

    // Example: Scrape only fruits
    console.log("🚀 Starting scraping for Blox Fruits...")
    const fruits = await scraper.scrapeCategory("Blox_Fruits", "fruit")

    if (fruits.length > 0) {
      const collection = db.collection("fruits")
      await collection.deleteMany({}) // Clear existing data
      await collection.insertMany(fruits)
      console.log(`💾 Saved ${fruits.length} fruits to database`)
    } else {
      console.warn("⚠️ No fruits scraped.")
    }

    console.log("🎉 Scraping completed!")
  } catch (error) {
    console.error("❌ Error during scraping:", error)
  } finally {
    await client.close()
  }
}

runScraper().catch(console.error)
