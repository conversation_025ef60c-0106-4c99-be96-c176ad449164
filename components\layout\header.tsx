"use client"

import Link from "next/link"
import { useState } from "react"
import { Menu, X, Calculator, Database, TrendingUp, BookOpen, ChevronDown } from "lucide-react"
import { Button } from "@/components/ui/button"

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isCalculatorsOpen, setIsCalculatorsOpen] = useState(false)

  const calculators = [
    { name: "Stats Calculator", href: "/calculator/stats" },
    { name: "Damage Calculator", href: "/calculator/damage" },
    { name: "Farming Calculator", href: "/calculator/farming" },
    { name: "Trading Calculator", href: "/calculator/trading" },
  ]

  const navigation = [
    { name: "Trading", href: "/trading", icon: TrendingUp },
    { name: "Database", href: "/database", icon: Database },
    { name: "Blog", href: "/blog", icon: BookOpen },
  ]

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
              <span className="text-white font-bold text-sm">BF</span>
            </div>
            <span className="font-gaming text-xl font-bold neon-text">Blox Fruits Calculator</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {/* Calculators Dropdown */}
            <div className="relative">
              <button
                onClick={() => setIsCalculatorsOpen(!isCalculatorsOpen)}
                className="flex items-center space-x-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
              >
                <Calculator className="w-4 h-4" />
                <span>Calculators</span>
                <ChevronDown className={`w-3 h-3 transition-transform ${isCalculatorsOpen ? "rotate-180" : ""}`} />
              </button>

              {isCalculatorsOpen && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-background border border-border rounded-md shadow-lg z-50">
                  <div className="py-2">
                    {calculators.map((calc) => (
                      <Link
                        key={calc.name}
                        href={calc.href}
                        className="block px-4 py-2 text-sm text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
                        onClick={() => setIsCalculatorsOpen(false)}
                      >
                        {calc.name}
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Other Navigation Items */}
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="flex items-center space-x-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
              >
                <item.icon className="w-4 h-4" />
                <span>{item.name}</span>
              </Link>
            ))}
          </nav>

          {/* Mobile Menu Button */}
          <Button variant="ghost" size="sm" className="md:hidden" onClick={() => setIsMenuOpen(!isMenuOpen)}>
            {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-border/40">
            <nav className="flex flex-col space-y-3">
              {/* Mobile Calculators */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm font-medium text-foreground">
                  <Calculator className="w-4 h-4" />
                  <span>Calculators</span>
                </div>
                <div className="ml-6 space-y-2">
                  {calculators.map((calc) => (
                    <Link
                      key={calc.name}
                      href={calc.href}
                      className="block text-sm text-muted-foreground hover:text-foreground transition-colors"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {calc.name}
                    </Link>
                  ))}
                </div>
              </div>

              {/* Other Mobile Navigation */}
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="flex items-center space-x-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <item.icon className="w-4 h-4" />
                  <span>{item.name}</span>
                </Link>
              ))}
            </nav>
          </div>
        )}
      </div>

      {/* Backdrop for dropdown */}
      {isCalculatorsOpen && <div className="fixed inset-0 z-40" onClick={() => setIsCalculatorsOpen(false)} />}
    </header>
  )
}
