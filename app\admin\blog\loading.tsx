import { Card, CardContent } from "@/components/ui/card"

export default function AdminBlogLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between mb-8">
          <div className="h-8 bg-slate-600 rounded w-64 animate-pulse"></div>
          <div className="h-10 bg-slate-600 rounded w-32 animate-pulse"></div>
        </div>

        {/* Stats Skeleton */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-4 text-center">
                <div className="h-8 bg-slate-600 rounded mb-2 animate-pulse"></div>
                <div className="h-4 bg-slate-600 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Filters Skeleton */}
        <Card className="mb-8 bg-slate-800/50 border-slate-700">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 h-10 bg-slate-600 rounded animate-pulse"></div>
              <div className="flex gap-2">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-10 bg-slate-600 rounded w-20 animate-pulse"></div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Posts List Skeleton */}
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i} className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="h-5 bg-slate-600 rounded w-16 animate-pulse"></div>
                      <div className="h-5 bg-slate-600 rounded w-20 animate-pulse"></div>
                      <div className="h-5 bg-slate-600 rounded w-16 animate-pulse"></div>
                    </div>
                    <div className="h-6 bg-slate-600 rounded mb-2 w-3/4 animate-pulse"></div>
                    <div className="h-4 bg-slate-600 rounded mb-4 animate-pulse"></div>
                    <div className="flex items-center gap-4">
                      <div className="h-4 bg-slate-600 rounded w-24 animate-pulse"></div>
                      <div className="h-4 bg-slate-600 rounded w-20 animate-pulse"></div>
                      <div className="h-4 bg-slate-600 rounded w-16 animate-pulse"></div>
                      <div className="h-4 bg-slate-600 rounded w-16 animate-pulse"></div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <div className="h-8 bg-slate-600 rounded w-8 animate-pulse"></div>
                    <div className="h-8 bg-slate-600 rounded w-8 animate-pulse"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
