import { MongoClient } from "mongodb"
import dotenv from "dotenv"

dotenv.config({ path: ".env.local" })

async function setupDatabase() {
  const client = new MongoClient(process.env.MONGODB_URL!)

  try {
    await client.connect()
    console.log("✅ Connected to MongoDB")

    const db = client.db("bloxfruits")

    // Créer les collections avec indexes
    const collections = [
      "fruits",
      "swords",
      "accessories",
      "guns",
      "materials",
      "fighting_styles", // Ajouté
      "tradingValues",
      "npcs",
      "quests",
      "raids",
      "mechanics", // Corrigé de gameMechanics à mechanics
      "codes",
      "events",
      "races", // Ajouté
      "locations", // Ajouté
    ]

    for (const collectionName of collections) {
      await db.createCollection(collectionName)
      console.log(`✅ Created collection: ${collectionName}`)
    }

    // Créer des indexes pour optimiser les requêtes
    await db.collection("fruits").createIndex({ name: 1 })
    await db.collection("swords").createIndex({ name: 1 })
    await db.collection("accessories").createIndex({ name: 1 })
    await db.collection("fighting_styles").createIndex({ name: 1 })
    await db.collection("tradingValues").createIndex({ itemName: 1, itemType: 1 })
    await db.collection("npcs").createIndex({ name: 1, sea: 1 })
    await db.collection("quests").createIndex({ sea: 1, levelRequirement: 1 })

    console.log("✅ Database setup completed!")
  } catch (error) {
    console.error("❌ Database setup failed:", error)
  } finally {
    await client.close()
  }
}

setupDatabase()
