import { type NextRequest, NextResponse } from "next/server"
import { BlogService } from "@/lib/models/blog"
import { connectToDatabase } from "@/lib/mongodb"

export async function GET(request: NextRequest) {
  try {
    await connectToDatabase()

    const { searchParams } = new URL(request.url)
    const page = Number.parseInt(searchParams.get("page") || "1")
    const limit = Number.parseInt(searchParams.get("limit") || "10")
    const category = searchParams.get("category") || undefined
    const search = searchParams.get("search") || undefined
    const status = (searchParams.get("status") as "draft" | "published" | "all") || "published"

    const result = await BlogService.getPosts({
      status,
      category,
      limit,
      skip: (page - 1) * limit,
      search,
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error("Error fetching blog posts:", error)
    return NextResponse.json({ error: "Failed to fetch blog posts" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    await connectToDatabase()

    const body = await request.json()

    // Validate required fields
    if (!body.title || !body.content || !body.excerpt) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    const post = await BlogService.createPost(body)

    return NextResponse.json(post, { status: 201 })
  } catch (error) {
    console.error("Error creating blog post:", error)
    return NextResponse.json({ error: "Failed to create blog post" }, { status: 500 })
  }
}
