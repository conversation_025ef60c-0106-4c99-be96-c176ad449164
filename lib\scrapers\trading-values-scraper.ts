import { MongoClient } from "mongodb"

interface TradingValue {
  itemName: string
  itemType: "fruit" | "sword" | "accessory" | "gun"
  currentValue: number
  previousValue: number
  trend: "up" | "down" | "stable"
  changePercent: number
  confidence: number
  volume: number
  lastUpdated: Date
  source: string
}

export class TradingValuesScraper {
  private baseUrl = "https://blox-fruits.fandom.com/api.php"
  private mongoClient: MongoClient

  constructor(mongoUrl: string) {
    this.mongoClient = new MongoClient(mongoUrl)
  }

  private async getPageContent(title: string): Promise<string | null> {
    const params = new URLSearchParams({
      action: "query",
      titles: title,
      prop: "revisions",
      rvprop: "content",
      format: "json",
    })

    try {
      const response = await fetch(`${this.baseUrl}?${params}`)
      const data = await response.json()

      if (!data.query?.pages) return null

      const pages = data.query.pages
      const pageId = Object.keys(pages)[0]
      const page = pages[pageId]

      if (page.missing || !page.revisions?.[0]) {
        return null
      }

      return page.revisions[0]["*"]
    } catch (error) {
      console.error("Error fetching page content:", error)
      return null
    }
  }

  private extractTradingValues(wikitext: string): TradingValue[] {
    const values: TradingValue[] = []

    // Chercher les tableaux de valeurs de trading
    const tablePattern = /\{\|[\s\S]*?\|\}/g
    const tables = wikitext.match(tablePattern) || []

    for (const table of tables) {
      // Extraire les lignes du tableau
      const rows = table.split("|-").slice(1) // Ignorer l'en-tête

      for (const row of rows) {
        const cells = row
          .split("|")
          .map((cell) => cell.trim())
          .filter((cell) => cell)

        if (cells.length >= 3) {
          const itemName = this.cleanText(cells[0])
          const valueText = this.cleanText(cells[1])
          const trendText = this.cleanText(cells[2])

          const currentValue = this.parseValue(valueText)
          if (currentValue > 0 && itemName) {
            values.push({
              itemName,
              itemType: this.determineItemType(itemName),
              currentValue,
              previousValue: currentValue, // À améliorer avec l'historique
              trend: this.parseTrend(trendText),
              changePercent: 0, // À calculer avec l'historique
              confidence: 75, // Score de confiance par défaut
              volume: 0,
              lastUpdated: new Date(),
              source: "Fandom Wiki Trading Page",
            })
          }
        }
      }
    }

    return values
  }

  private cleanText(text: string): string {
    return text
      .replace(/\[\[([^\]|]+)(\|[^\]]+)?\]\]/g, "$1")
      .replace(/\{\{[^}]*\}\}/g, "")
      .replace(/'''?([^']+)'''?/g, "$1")
      .trim()
  }

  private parseValue(valueText: string): number {
    // Extraire la valeur numérique
    const match = valueText.match(/(\d+(?:,\d+)*(?:\.\d+)?)/)
    if (match) {
      return Number.parseFloat(match[1].replace(/,/g, ""))
    }
    return 0
  }

  private determineItemType(itemName: string): TradingValue["itemType"] {
    const fruitKeywords = ["fruit", "dragon", "buddha", "venom", "dough", "shadow"]
    const swordKeywords = ["sword", "blade", "katana", "cutlass"]
    const gunKeywords = ["gun", "rifle", "pistol", "cannon"]

    const name = itemName.toLowerCase()

    if (fruitKeywords.some((keyword) => name.includes(keyword))) return "fruit"
    if (swordKeywords.some((keyword) => name.includes(keyword))) return "sword"
    if (gunKeywords.some((keyword) => name.includes(keyword))) return "gun"

    return "accessory" // Par défaut
  }

  private parseTrend(trendText: string): TradingValue["trend"] {
    const text = trendText.toLowerCase()
    if (text.includes("up") || text.includes("↑") || text.includes("increase")) return "up"
    if (text.includes("down") || text.includes("↓") || text.includes("decrease")) return "down"
    return "stable"
  }

  async scrapeTradingValues(): Promise<TradingValue[]> {
    console.log("🔍 Scraping trading values...")

    const tradingPages = ["Trading", "Fruit Trading", "Value List", "Trading Values", "Market Values"]

    const allValues: TradingValue[] = []

    for (const pageName of tradingPages) {
      console.log(`Checking page: ${pageName}`)

      const content = await this.getPageContent(pageName)
      if (content) {
        const values = this.extractTradingValues(content)
        allValues.push(...values)
        console.log(`Found ${values.length} values in ${pageName}`)
      }
    }

    return allValues
  }

  async updateDatabase(): Promise<void> {
    try {
      await this.mongoClient.connect()
      const db = this.mongoClient.db("bloxfruits")
      const collection = db.collection("tradingvalues")

      const values = await this.scrapeTradingValues()

      if (values.length > 0) {
        // Supprimer les anciennes valeurs
        await collection.deleteMany({})

        // Insérer les nouvelles valeurs
        await collection.insertMany(values)

        console.log(`✅ Updated ${values.length} trading values in database`)
      } else {
        console.log("⚠️ No trading values found")
      }
    } catch (error) {
      console.error("❌ Error updating trading values:", error)
    } finally {
      await this.mongoClient.close()
    }
  }
}
