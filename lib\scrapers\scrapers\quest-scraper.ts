import { BaseScraper } from "../base-scraper"
import { ScrapedItem, QuestData } from "../types"

export class Quest<PERSON><PERSON>raper extends BaseScraper {
  
  extractSpecificData(wikitext: string, infoboxData: Record<string, string>): QuestData {
    const questData: any = {}

    // Extract quest giver from title or content
    const questGiverMatch = wikitext.match(/'''([^']+)'''\s+is\s+an?\s+\[\[NPC\]\]/)
    if (questGiverMatch) {
      questData.questGiver = questGiverMatch[1]
    }

    // Also try to extract from infobox
    if (infoboxData.questgiver || infoboxData.giver) {
      questData.questGiver = infoboxData.questgiver || infoboxData.giver
    }

    // Extract requirements
    const requirementsMatch = wikitext.match(/===?\s*Requirements\s*===?([\s\S]*?)(?===|$)/i)
    if (requirementsMatch) {
      const requirements: Array<{ type: string; description: string; amount?: number }> = []
      const reqContent = requirementsMatch[1]

      const reqLines = reqContent.split('\n').filter(line => line.trim().startsWith('*'))

      reqLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 5) {
          // Extract amounts
          const moneyMatch = cleanLine.match(/\{\{Money\|([^}]+)\}\}/)
          const fragmentMatch = cleanLine.match(/\{\{Fragment\|([^}]+)\}\}/)

          if (moneyMatch) {
            const amount = this.parseNumber(moneyMatch[1])
            requirements.push({
              type: "Money",
              description: cleanLine,
              amount: amount
            })
          } else if (fragmentMatch) {
            const amount = this.parseNumber(fragmentMatch[1])
            requirements.push({
              type: "Fragment",
              description: cleanLine,
              amount: amount
            })
          } else if (cleanLine.toLowerCase().includes('level')) {
            // Extract level requirements
            const levelMatch = cleanLine.match(/level\s*(\d+)/i)
            if (levelMatch) {
              requirements.push({
                type: "Level",
                description: cleanLine,
                amount: parseInt(levelMatch[1])
              })
            } else {
              requirements.push({
                type: "Level",
                description: cleanLine
              })
            }
          } else {
            requirements.push({
              type: "General",
              description: cleanLine
            })
          }
        }
      })

      if (requirements.length > 0) {
        questData.requirements = requirements
      }
    }

    // Extract quest steps
    const flowerQuestMatch = wikitext.match(/===?\s*Flower Quest\s*===?([\s\S]*?)(?===|$)/i)
    if (flowerQuestMatch) {
      const steps: string[] = []
      const stepContent = flowerQuestMatch[1]

      const stepLines = stepContent.split('\n').filter(line => line.trim().startsWith('*'))

      stepLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 10) {
          steps.push(cleanLine)
        }
      })

      if (steps.length > 0) {
        questData.steps = steps
      }
    }

    // Extract general quest steps
    const stepsMatch = wikitext.match(/===?\s*Steps\s*===?([\s\S]*?)(?===|$)/i)
    if (stepsMatch && !questData.steps) {
      const steps: string[] = []
      const stepContent = stepsMatch[1]

      const stepLines = stepContent.split('\n').filter(line => line.trim().startsWith('*') || line.trim().match(/^\d+\./))

      stepLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace(/^\*\s*|\d+\.\s*/, '').trim())
        if (cleanLine.length > 10) {
          steps.push(cleanLine)
        }
      })

      if (steps.length > 0) {
        questData.steps = steps
      }
    }

    // Extract rewards
    const rewardsMatch = wikitext.match(/===?\s*Rewards?\s*===?([\s\S]*?)(?===|$)/i)
    if (rewardsMatch) {
      const rewards: Array<{ type: string; description: string; amount?: number }> = []
      const rewardContent = rewardsMatch[1]

      const rewardLines = rewardContent.split('\n').filter(line => line.trim().startsWith('*'))

      rewardLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 5) {
          // Extract monetary rewards
          const moneyMatch = cleanLine.match(/\{\{Money\|([^}]+)\}\}/)
          const fragmentMatch = cleanLine.match(/\{\{Fragment\|([^}]+)\}\}/)

          if (moneyMatch) {
            const amount = this.parseNumber(moneyMatch[1])
            rewards.push({
              type: "Money",
              description: cleanLine,
              amount: amount
            })
          } else if (fragmentMatch) {
            const amount = this.parseNumber(fragmentMatch[1])
            rewards.push({
              type: "Fragment",
              description: cleanLine,
              amount: amount
            })
          } else if (cleanLine.toLowerCase().includes('exp')) {
            // Extract experience rewards
            const expMatch = cleanLine.match(/(\d+)\s*exp/i)
            if (expMatch) {
              rewards.push({
                type: "Experience",
                description: cleanLine,
                amount: parseInt(expMatch[1])
              })
            } else {
              rewards.push({
                type: "Experience",
                description: cleanLine
              })
            }
          } else {
            rewards.push({
              type: "General",
              description: cleanLine
            })
          }
        }
      })

      if (rewards.length > 0) {
        questData.rewards = rewards
      }
    }

    // Extract tips
    const tips: string[] = []
    const tipMatches = wikitext.match(/\*?\s*Tip:([^*\n]+)/gi)
    if (tipMatches) {
      tipMatches.forEach(tip => {
        const cleanTip = this.cleanWikitext(tip.replace(/\*?\s*Tip:\s*/i, '').trim())
        if (cleanTip.length > 10) {
          tips.push(cleanTip)
        }
      })
    }

    // Also look for notes section
    const notesMatch = wikitext.match(/===?\s*Notes?\s*===?([\s\S]*?)(?===|$)/i)
    if (notesMatch) {
      const noteLines = notesMatch[1].split('\n').filter(line => line.trim().startsWith('*'))
      noteLines.forEach(line => {
        const cleanNote = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanNote.length > 10) {
          tips.push(cleanNote)
        }
      })
    }

    if (tips.length > 0) {
      questData.tips = [...new Set(tips)].slice(0, 10) // Remove duplicates, limit to 10
    }

    // Estimate difficulty based on requirements
    if (questData.requirements) {
      const hasMoneyReq = questData.requirements.some((req: any) => req.type === "Money" && req.amount && req.amount > 1000000)
      const hasComplexReq = questData.requirements.some((req: any) => 
        req.description.toLowerCase().includes("defeat") || 
        req.description.toLowerCase().includes("kill") ||
        req.description.toLowerCase().includes("boss")
      )
      const hasHighLevelReq = questData.requirements.some((req: any) => req.type === "Level" && req.amount && req.amount > 1000)

      if ((hasMoneyReq && hasComplexReq) || hasHighLevelReq) {
        questData.difficulty = "Extreme"
      } else if (hasMoneyReq && hasComplexReq) {
        questData.difficulty = "Hard"
      } else if (hasMoneyReq || hasComplexReq) {
        questData.difficulty = "Medium"
      } else {
        questData.difficulty = "Easy"
      }
    }

    // Estimate time based on complexity
    if (questData.steps) {
      const stepCount = questData.steps.length
      if (stepCount > 10) {
        questData.estimatedTime = "30-60 minutes"
      } else if (stepCount > 5) {
        questData.estimatedTime = "15-30 minutes"
      } else {
        questData.estimatedTime = "5-15 minutes"
      }
    }

    return questData
  }

  async scrapeItem(title: string, itemType: string): Promise<ScrapedItem | null> {
    console.log(`📋 Scraping quest: ${title}`)

    const wikitext = await this.getPageContent(title)
    if (!wikitext) {
      console.warn(`⚠️ No content found for ${title}`)
      return null
    }

    // Extract infobox data
    const infoboxData = this.extractTemplateData(wikitext, "Infobox")

    // Extract quest-specific data
    const questData = this.extractSpecificData(wikitext, infoboxData)

    // Create the scraped item
    const item: ScrapedItem = {
      name: title,
      type: itemType,
      category: "quest",
      wikiUrl: `https://blox-fruits.fandom.com/wiki/${encodeURIComponent(title)}`,
      lastUpdated: new Date(),
      questData,
      rawData: {
        infobox: infoboxData,
        wikitextLength: wikitext.length,
        movesFound: 0,
        statsFound: 0,
        extractedAt: new Date().toISOString(),
        questData
      }
    }

    // Extract basic properties from infobox
    if (infoboxData.description) item.description = this.cleanWikitext(infoboxData.description)
    if (infoboxData.location) item.location = infoboxData.location

    return item
  }

  async scrapeCategory(categoryName: string = "Quests"): Promise<ScrapedItem[]> {
    console.log(`\n🎯 Starting to scrape quests from category: ${categoryName}`)

    const members = await this.getCategoryMembers(categoryName)
    if (members.length === 0) {
      return []
    }

    const items: ScrapedItem[] = []
    let processed = 0

    for (const member of members) {
      try {
        const item = await this.scrapeItem(member.title, "quest")
        if (item) {
          items.push(item)
        }
        processed++

        // Progress indicator
        if (processed % 5 === 0 || processed === members.length) {
          console.log(
            `📊 Progress: ${processed}/${members.length} (${Math.round((processed / members.length) * 100)}%)`
          )
        }

        // Small delay between items
        await new Promise((resolve) => setTimeout(resolve, 200))
      } catch (error) {
        console.error(`❌ Error scraping quest ${member.title}:`, error)
      }
    }

    console.log(`✅ Completed quests: ${items.length}/${members.length} items scraped`)
    return items
  }
}
