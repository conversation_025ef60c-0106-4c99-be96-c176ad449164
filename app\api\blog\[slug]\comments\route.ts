import { type NextRequest, NextResponse } from "next/server"
import { BlogService } from "@/lib/models/blog"
import { connectToDatabase } from "@/lib/mongodb"

export async function GET(request: NextRequest, { params }: { params: { slug: string } }) {
  try {
    await connectToDatabase()

    // First get the post to get its ID
    const post = await BlogService.getPostBySlug(params.slug)
    if (!post) {
      return NextResponse.json({ error: "Post not found" }, { status: 404 })
    }

    const comments = await BlogService.getComments(post._id)

    return NextResponse.json(comments)
  } catch (error) {
    console.error("Error fetching comments:", error)
    return NextResponse.json({ error: "Failed to fetch comments" }, { status: 500 })
  }
}

export async function POST(request: NextRequest, { params }: { params: { slug: string } }) {
  try {
    await connectToDatabase()

    const body = await request.json()

    // First get the post to get its ID
    const post = await BlogService.getPostBySlug(params.slug)
    if (!post) {
      return NextResponse.json({ error: "Post not found" }, { status: 404 })
    }

    // Validate required fields
    if (!body.author?.name || !body.author?.email || !body.content) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    const commentData = {
      ...body,
      postId: post._id,
    }

    const comment = await BlogService.createComment(commentData)

    return NextResponse.json(comment, { status: 201 })
  } catch (error) {
    console.error("Error creating comment:", error)
    return NextResponse.json({ error: "Failed to create comment" }, { status: 500 })
  }
}
