#!/bin/bash

# Define the lock file path
LOCK_FILE="/tmp/blox_fruits_scraper.lock"

# Check if the lock file exists
if [ -f "$LOCK_FILE" ]; then
  echo "Scraper is already running. Exiting."
  exit 0
fi

# Create the lock file
touch "$LOCK_FILE"

echo "Starting Blox Fruits hourly scraper..."

# Navigate to the project directory (adjust if necessary)
# cd /path/to/your/project

# Run the comprehensive scraper script
# Ensure you have pnpm installed and your dependencies are up to date
pnpm run scrape-all

# Check the exit status of the scraper
if [ $? -eq 0 ]; then
  echo "Blox Fruits hourly scraper completed successfully."
else
  echo "Blox Fruits hourly scraper failed."
fi

# Remove the lock file
rm -f "$LOCK_FILE"

exit 0
