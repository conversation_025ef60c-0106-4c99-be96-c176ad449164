import { notFound } from "next/navigation"
import Link from "next/link"
import { tradingValuesData } from "@/lib/data"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ChevronLeft, TrendingUp, TrendingDown, Minus, BarChart, History, Info } from "lucide-react"
import ValueHistoryChart from "@/components/trading/value-history-chart"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

const slugify = (text: string) =>
  text
    .toString()
    .toLowerCase()
    .replace(/\s+/g, "-")
    .replace(/[^\w-]+/g, "")
    .replace(/--+/g, "-")
    .replace(/^-+/, "")
    .replace(/-+$/, "")

const getItemData = async (slug: string) => {
  return tradingValuesData.find((item) => slugify(item.name) === slug)
}

const generateHistory = (item: NonNullable<Awaited<ReturnType<typeof getItemData>>>) => {
  const history = []
  let currentValue = item.currentValue
  for (let i = 0; i < 30; i++) {
    const date = new Date()
    date.setDate(date.getDate() - i)
    const fluctuation = (Math.random() - 0.5) * 0.1 * currentValue // +/- 5%
    currentValue -= fluctuation
    if (item.trend === "up") {
      currentValue -= (item.changePercent / 30 / 100) * currentValue
    } else if (item.trend === "down") {
      currentValue += (Math.abs(item.changePercent) / 30 / 100) * currentValue
    }
    history.push({
      date: date.toLocaleDateString("en-CA"), // YYYY-MM-DD
      value: Math.round(currentValue),
    })
  }
  return history.reverse()
}

const getTrendIcon = (trend: string) => {
  if (trend === "up") return <TrendingUp className="w-4 h-4 text-green-400" />
  if (trend === "down") return <TrendingDown className="w-4 h-4 text-red-400" />
  return <Minus className="w-4 h-4 text-gray-400" />
}

const getTrendColor = (trend: string) => {
  if (trend === "up") return "text-green-400"
  if (trend === "down") return "text-red-400"
  return "text-gray-400"
}

const getRarityColor = (rarity: string) => {
  switch (rarity) {
    case "Common":
      return "bg-gray-500/20 text-gray-400"
    case "Uncommon":
      return "bg-green-500/20 text-green-400"
    case "Rare":
      return "bg-blue-500/20 text-blue-400"
    case "Legendary":
      return "bg-purple-500/20 text-purple-400"
    case "Mythical":
      return "bg-yellow-500/20 text-yellow-400"
    default:
      return "bg-gray-500/20 text-gray-400"
  }
}

export default async function ItemHistoryPage({ params }: { params: { itemName: string } }) {
  const item = await getItemData(params.itemName)

  if (!item) {
    notFound()
  }

  const history = generateHistory(item)

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Button asChild variant="outline" size="sm">
          <Link href="/trading">
            <ChevronLeft className="w-4 h-4 mr-2" />
            Back to Trading
          </Link>
        </Button>
      </div>

      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="font-gaming text-4xl font-bold">{item.name}</h1>
          <div className="flex items-center gap-2 mt-2">
            <Badge variant="outline" className="text-sm capitalize">
              {item.type}
            </Badge>
            <Badge className={`text-sm ${getRarityColor(item.rarity)}`}>{item.rarity}</Badge>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card className="gaming-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Value</CardTitle>
            <Info className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold font-mono text-primary">{item.currentValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Last updated: {item.lastUpdated.toLocaleDateString()}</p>
          </CardContent>
        </Card>
        <Card className="gaming-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">24h Change</CardTitle>
            {getTrendIcon(item.trend)}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getTrendColor(item.trend)}`}>
              {item.changePercent > 0 ? "+" : ""}
              {item.changePercent.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">From {item.previousValue.toLocaleString()}</p>
          </CardContent>
        </Card>
        <Card className="gaming-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Trading Volume</CardTitle>
            <BarChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold font-mono">{item.volume.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Last 24 hours</p>
          </CardContent>
        </Card>
        <Card className="gaming-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Confidence</CardTitle>
            <History className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div
              className={`text-2xl font-bold ${item.confidence >= 90 ? "text-green-400" : item.confidence >= 80 ? "text-yellow-400" : "text-red-400"}`}
            >
              {item.confidence}%
            </div>
            <p className="text-xs text-muted-foreground">Based on market data</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <Card className="gaming-card">
            <CardHeader>
              <CardTitle>Value History (30 Days)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <ValueHistoryChart data={history} />
              </div>
            </CardContent>
          </Card>
        </div>
        <div>
          <Card className="gaming-card">
            <CardHeader>
              <CardTitle>Price History</CardTitle>
            </CardHeader>
            <CardContent className="max-h-[400px] overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Value</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {history
                    .slice()
                    .reverse()
                    .map((entry) => (
                      <TableRow key={entry.date}>
                        <TableCell>{new Date(entry.date).toLocaleDateString()}</TableCell>
                        <TableCell className="text-right font-mono">{entry.value.toLocaleString()}</TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
