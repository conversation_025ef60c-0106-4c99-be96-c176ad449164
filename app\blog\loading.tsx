import { Card, CardContent } from "@/components/ui/card"
import { BookOpen } from "lucide-react"

export default function BlogLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header Skeleton */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <BookOpen className="w-10 h-10 text-primary animate-pulse" />
            <div className="h-10 bg-slate-600 rounded w-64 animate-pulse"></div>
          </div>
          <div className="h-6 bg-slate-600 rounded w-96 animate-pulse"></div>
        </div>

        {/* Stats Skeleton */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-4 text-center">
                <div className="w-8 h-8 bg-slate-600 rounded mx-auto mb-2 animate-pulse"></div>
                <div className="h-6 bg-slate-600 rounded mb-2 animate-pulse"></div>
                <div className="h-4 bg-slate-600 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Featured Posts Skeleton */}
        <div className="mb-8">
          <div className="h-8 bg-slate-600 rounded w-48 mb-4 animate-pulse"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[...Array(2)].map((_, i) => (
              <Card key={i} className="bg-slate-800/50 border-slate-700">
                <CardContent className="p-0">
                  <div className="h-48 bg-slate-600 rounded-t-lg animate-pulse"></div>
                  <div className="p-6">
                    <div className="flex gap-2 mb-3">
                      <div className="h-6 bg-slate-600 rounded w-20 animate-pulse"></div>
                      <div className="h-6 bg-slate-600 rounded w-16 animate-pulse"></div>
                    </div>
                    <div className="h-6 bg-slate-600 rounded mb-2 animate-pulse"></div>
                    <div className="h-4 bg-slate-600 rounded mb-4 animate-pulse"></div>
                    <div className="flex justify-between">
                      <div className="h-4 bg-slate-600 rounded w-32 animate-pulse"></div>
                      <div className="h-4 bg-slate-600 rounded w-24 animate-pulse"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Filters Skeleton */}
        <Card className="mb-8 bg-slate-800/50 border-slate-700">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 h-10 bg-slate-600 rounded animate-pulse"></div>
              <div className="flex gap-2">
                <div className="h-10 bg-slate-600 rounded w-40 animate-pulse"></div>
                <div className="h-10 bg-slate-600 rounded w-40 animate-pulse"></div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Articles Grid Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="bg-slate-800/50 border-slate-700">
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="flex gap-2 mb-3">
                    <div className="h-5 bg-slate-600 rounded w-16"></div>
                    <div className="h-5 bg-slate-600 rounded w-20"></div>
                  </div>
                  <div className="h-6 bg-slate-600 rounded mb-2"></div>
                  <div className="h-4 bg-slate-600 rounded mb-4"></div>
                  <div className="flex gap-2 mb-4">
                    <div className="h-5 bg-slate-600 rounded w-12"></div>
                    <div className="h-5 bg-slate-600 rounded w-16"></div>
                    <div className="h-5 bg-slate-600 rounded w-14"></div>
                  </div>
                  <div className="flex justify-between">
                    <div className="h-4 bg-slate-600 rounded w-20"></div>
                    <div className="h-4 bg-slate-600 rounded w-24"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
