"use client"

import { useEffect, useRef } from "react"

interface AdBannerProps {
  adSlot: string
  adFormat?: "auto" | "rectangle" | "vertical" | "horizontal"
  fullWidthResponsive?: boolean
  className?: string
}

export function AdBanner({ adSlot, adFormat = "auto", fullWidthResponsive = true, className = "" }: AdBannerProps) {
  const adRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    try {
      // Google AdSense
      if (typeof window !== "undefined" && window.adsbygoogle) {
        window.adsbygoogle.push({})
      }
    } catch (error) {
      console.error("AdSense error:", error)
    }
  }, [])

  return (
    <div className={`ad-container ${className}`} ref={adRef}>
      <ins
        className="adsbygoogle"
        style={{ display: "block" }}
        data-ad-client={process.env.NEXT_PUBLIC_ADSENSE_CLIENT_ID}
        data-ad-slot={adSlot}
        data-ad-format={adFormat}
        data-full-width-responsive={fullWidthResponsive}
      />
    </div>
  )
}

// Composant pour Mediavine (si vous avez le trafic requis)
export function MediavineAd({ adId, className = "" }: { adId: string; className?: string }) {
  useEffect(() => {
    // Mediavine script loading
    if (typeof window !== "undefined" && window.googletag) {
      window.googletag.cmd.push(() => {
        window.googletag.display(adId)
      })
    }
  }, [adId])

  return (
    <div className={`mediavine-ad ${className}`}>
      <div id={adId} />
    </div>
  )
}

// Types pour TypeScript
declare global {
  interface Window {
    adsbygoogle: any[]
    googletag: any
  }
}
