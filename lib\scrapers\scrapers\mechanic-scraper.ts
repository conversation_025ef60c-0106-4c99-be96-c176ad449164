import { BaseScraper } from "../base-scraper"
import { ScrapedItem, MechanicData } from "../types"

export class MechanicScraper extends BaseScraper {
  
  extractSpecificData(wikitext: string, infoboxData: Record<string, string>): MechanicData {
    const mechanicData: any = {}

    // Extract purpose from infobox
    if (infoboxData.purpose || infoboxData["purpose:"]) {
      mechanicData.purpose = infoboxData.purpose || infoboxData["purpose:"]
    }

    // Extract triggered by from infobox
    if (infoboxData.triggered_by || infoboxData["triggered_by:"]) {
      mechanicData.triggeredBy = infoboxData.triggered_by || infoboxData["triggered_by:"]
    }

    // Extract mechanics from Mechanics section
    const mechanicsMatch = wikitext.match(/==\s*Mechanics\s*==([\s\S]*?)(?===|$)/i)
    if (mechanicsMatch) {
      const mechanics: string[] = []
      const mechanicsContent = mechanicsMatch[1]

      // Extract mechanic paragraphs
      const mechanicParagraphs = mechanicsContent.split('\n\n').filter(para => para.trim().length > 20)
      mechanicParagraphs.forEach(para => {
        const cleanPara = this.cleanWikitext(para.trim())
        if (cleanPara.length > 20) {
          mechanics.push(cleanPara)
        }
      })

      // Also extract from bullet points
      const mechanicLines = mechanicsContent.split('\n').filter(line => line.trim().startsWith('*'))
      mechanicLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 15) {
          mechanics.push(cleanLine)
        }
      })

      if (mechanics.length > 0) {
        mechanicData.mechanics = [...new Set(mechanics)] // Remove duplicates
      }
    }

    // Extract from How it Works section
    const howItWorksMatch = wikitext.match(/==\s*How it Works\s*==([\s\S]*?)(?===|$)/i)
    if (howItWorksMatch && !mechanicData.mechanics) {
      const mechanics: string[] = []
      const howItWorksContent = howItWorksMatch[1]

      const workingParagraphs = howItWorksContent.split('\n\n').filter(para => para.trim().length > 20)
      workingParagraphs.forEach(para => {
        const cleanPara = this.cleanWikitext(para.trim())
        if (cleanPara.length > 20) {
          mechanics.push(cleanPara)
        }
      })

      if (mechanics.length > 0) {
        mechanicData.mechanics = mechanics
      }
    }

    // Extract notes
    const notesMatch = wikitext.match(/==\s*Notes\s*==([\s\S]*?)(?===|$)/i)
    if (notesMatch) {
      const notes: string[] = []
      const notesContent = notesMatch[1]
      const noteLines = notesContent.split('\n').filter(line => line.trim().startsWith('*'))

      noteLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 15) {
          notes.push(cleanLine)
        }
      })

      if (notes.length > 0) {
        mechanicData.notes = notes
      }
    }

    // Extract restrictions (Marines, Pirates, etc.)
    const restrictions: string[] = []
    const restrictionPatterns = [
      /unable to ([^.]+)/gi,
      /cannot ([^.]+)/gi,
      /restricted to ([^.]+)/gi,
      /only ([^.]+) can/gi,
      /not available to ([^.]+)/gi,
      /exclusive to ([^.]+)/gi
    ]

    restrictionPatterns.forEach(pattern => {
      let match
      while ((match = pattern.exec(wikitext)) !== null) {
        const cleanRestriction = this.cleanWikitext(match[0])
        if (cleanRestriction.length > 10 && cleanRestriction.length < 100) {
          restrictions.push(cleanRestriction)
        }
      }
    })

    if (restrictions.length > 0) {
      mechanicData.restrictions = [...new Set(restrictions)].slice(0, 5)
    }

    // Extract trivia information
    const triviaMatch = wikitext.match(/==\s*Trivia\s*==([\s\S]*?)(?===|$)/i)
    if (triviaMatch) {
      const trivia: string[] = []
      const triviaContent = triviaMatch[1]
      const triviaLines = triviaContent.split('\n').filter(line => line.trim().startsWith('*'))

      triviaLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 15) {
          trivia.push(cleanLine)
        }
      })

      if (trivia.length > 0) {
        mechanicData.trivia = trivia
      }
    }

    // Extract purpose from content if not in infobox
    if (!mechanicData.purpose) {
      const purposePatterns = [
        /purpose of ([^.]+) is to ([^.]+)/gi,
        /([^.]+) is used to ([^.]+)/gi,
        /allows players to ([^.]+)/gi,
        /enables ([^.]+)/gi
      ]

      purposePatterns.forEach(pattern => {
        const match = wikitext.match(pattern)
        if (match && !mechanicData.purpose) {
          mechanicData.purpose = this.cleanWikitext(match[0])
        }
      })
    }

    // Extract triggered by from content if not in infobox
    if (!mechanicData.triggeredBy) {
      const triggerPatterns = [
        /triggered by ([^.]+)/gi,
        /activated when ([^.]+)/gi,
        /occurs when ([^.]+)/gi,
        /happens when ([^.]+)/gi
      ]

      triggerPatterns.forEach(pattern => {
        const match = wikitext.match(pattern)
        if (match && !mechanicData.triggeredBy) {
          mechanicData.triggeredBy = this.cleanWikitext(match[1])
        }
      })
    }

    // Extract requirements
    const requirementsMatch = wikitext.match(/==\s*Requirements\s*==([\s\S]*?)(?===|$)/i)
    if (requirementsMatch) {
      const requirements: string[] = []
      const reqContent = requirementsMatch[1]
      const reqLines = reqContent.split('\n').filter(line => line.trim().startsWith('*'))

      reqLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 10) {
          requirements.push(cleanLine)
        }
      })

      if (requirements.length > 0) {
        mechanicData.requirements = requirements
      }
    }

    // Extract effects
    const effectsMatch = wikitext.match(/==\s*Effects?\s*==([\s\S]*?)(?===|$)/i)
    if (effectsMatch) {
      const effects: string[] = []
      const effectContent = effectsMatch[1]
      const effectLines = effectContent.split('\n').filter(line => line.trim().startsWith('*'))

      effectLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 10) {
          effects.push(cleanLine)
        }
      })

      if (effects.length > 0) {
        mechanicData.effects = effects
      }
    }

    return mechanicData
  }

  async scrapeItem(title: string, itemType: string): Promise<ScrapedItem | null> {
    console.log(`⚙️ Scraping mechanic: ${title}`)

    const wikitext = await this.getPageContent(title)
    if (!wikitext) {
      console.warn(`⚠️ No content found for ${title}`)
      return null
    }

    // Extract infobox data
    const infoboxData = this.extractTemplateData(wikitext, "Infobox")

    // Extract mechanic-specific data
    const mechanicData = this.extractSpecificData(wikitext, infoboxData)

    // Create the scraped item
    const item: ScrapedItem = {
      name: title,
      type: itemType,
      category: "mechanic",
      wikiUrl: `https://blox-fruits.fandom.com/wiki/${encodeURIComponent(title)}`,
      lastUpdated: new Date(),
      mechanicData,
      rawData: {
        infobox: infoboxData,
        wikitextLength: wikitext.length,
        movesFound: 0,
        statsFound: 0,
        extractedAt: new Date().toISOString(),
        mechanicData
      }
    }

    // Extract basic properties from infobox
    if (infoboxData.description) item.description = this.cleanWikitext(infoboxData.description)

    return item
  }

  async scrapeCategory(categoryName: string = "Game_Mechanics"): Promise<ScrapedItem[]> {
    console.log(`\n🎯 Starting to scrape mechanics from category: ${categoryName}`)

    const members = await this.getCategoryMembers(categoryName)
    if (members.length === 0) {
      return []
    }

    const items: ScrapedItem[] = []
    let processed = 0

    for (const member of members) {
      try {
        const item = await this.scrapeItem(member.title, "mechanic")
        if (item) {
          items.push(item)
        }
        processed++

        // Progress indicator
        if (processed % 5 === 0 || processed === members.length) {
          console.log(
            `📊 Progress: ${processed}/${members.length} (${Math.round((processed / members.length) * 100)}%)`
          )
        }

        // Small delay between items
        await new Promise((resolve) => setTimeout(resolve, 200))
      } catch (error) {
        console.error(`❌ Error scraping mechanic ${member.title}:`, error)
      }
    }

    console.log(`✅ Completed mechanics: ${items.length}/${members.length} items scraped`)
    return items
  }
}
