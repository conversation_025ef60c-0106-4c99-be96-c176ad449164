"use client"

import { useState, useMemo } from "react"
import Link from "next/link"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Search, Filter, Database, ExternalLink, Star, TrendingUp, Shield, Zap, ArrowRight } from "lucide-react"

interface DatabaseItem {
  id: string
  name: string
  type: "fruit" | "sword" | "accessory" | "gun" | "material"
  rarity: "Common" | "Uncommon" | "Rare" | "Legendary" | "Mythical"
  imageUrl: string
  price?: string
  robuxPrice?: string
  obtainment?: string
  stats?: string[]
  description?: string
  damage?: number
  mastery?: number
  special?: string[]
  awakened?: boolean
  value?: string
}

export default function DatabasePage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState<string>("all")
  const [selectedRarity, setSelectedRarity] = useState<string>("all")
  const [sortBy, setSortBy] = useState<string>("name")

  // Base de données complète avec tous les items
  const databaseItems: DatabaseItem[] = [
    // FRUITS MYTHIQUES
    {
      id: "leopard-fruit",
      name: "Leopard",
      type: "fruit",
      rarity: "Mythical",
      imageUrl: "/placeholder.svg?height=100&width=100",
      price: "5,000,000",
      robuxPrice: "3,000",
      description: "The most expensive and powerful fruit in the game",
      stats: ["Highest Damage", "Speed Boost", "Stealth Mode", "Transformation"],
      damage: 95,
      mastery: 600,
      special: ["Afterimage Assault", "Body Flicker", "Spiraling Kick"],
      awakened: false,
      value: "5M",
    },
    {
      id: "dragon-fruit",
      name: "Dragon",
      type: "fruit",
      rarity: "Mythical",
      imageUrl: "/placeholder.svg?height=100&width=100",
      price: "3,500,000",
      robuxPrice: "2,600",
      description: "One of the most powerful Zoan-type Devil Fruits",
      stats: ["Extremely High Damage", "Flight Ability", "Transformation", "AOE Attacks"],
      damage: 90,
      mastery: 500,
      special: ["Dragon Rush", "Fire Shower", "Heatwave Beam"],
      awakened: true,
      value: "3.5M",
    },
    {
      id: "kitsune-fruit",
      name: "Kitsune",
      type: "fruit",
      rarity: "Mythical",
      imageUrl: "/placeholder.svg?height=100&width=100",
      price: "8,000,000",
      robuxPrice: "4,000",
      description: "Newest mythical fruit with fox transformation",
      stats: ["Very High Damage", "Illusion Powers", "Speed Boost", "Teleportation"],
      damage: 88,
      mastery: 550,
      special: ["Fox Fire", "Illusion Dash", "Spirit Bomb"],
      awakened: false,
      value: "8M",
    },
    {
      id: "t-rex-fruit",
      name: "T-Rex",
      type: "fruit",
      rarity: "Mythical",
      imageUrl: "/placeholder.svg?height=100&width=100",
      price: "2,700,000",
      robuxPrice: "2,200",
      description: "Ancient Zoan fruit with devastating power",
      stats: ["High Damage", "Transformation", "Roar Stun", "Charge Attack"],
      damage: 85,
      mastery: 450,
      special: ["Ancient Roar", "Predator", "Extinction"],
      awakened: false,
      value: "2.7M",
    },

    // FRUITS LÉGENDAIRES
    {
      id: "dough-fruit",
      name: "Dough",
      type: "fruit",
      rarity: "Mythical",
      imageUrl: "/placeholder.svg?height=100&width=100",
      price: "2,800,000",
      robuxPrice: "2,400",
      description: "Versatile paramecia with awakening potential",
      stats: ["High Damage", "Versatile Moves", "Combo Potential", "Awakening"],
      damage: 82,
      mastery: 500,
      special: ["Scorching Donut", "Restless Dough Barrage", "Carved Mochi"],
      awakened: true,
      value: "2.8M",
    },
    {
      id: "shadow-fruit",
      name: "Shadow",
      type: "fruit",
      rarity: "Legendary",
      imageUrl: "/placeholder.svg?height=100&width=100",
      price: "2,900,000",
      robuxPrice: "2,425",
      description: "Dark powers with shadow manipulation",
      stats: ["High Damage", "Shadow Clone", "Teleportation", "AOE"],
      damage: 80,
      mastery: 400,
      special: ["Shadow Travel", "Umbrage", "Nightmare Leech"],
      awakened: true,
      value: "2.9M",
    },
    {
      id: "venom-fruit",
      name: "Venom",
      type: "fruit",
      rarity: "Legendary",
      imageUrl: "/placeholder.svg?height=100&width=100",
      price: "3,000,000",
      robuxPrice: "2,450",
      description: "Poison-based logia with DOT damage",
      stats: ["High Damage", "Poison DOT", "Flight", "Transformation"],
      damage: 78,
      mastery: 450,
      special: ["Toxic Fog", "Venom Demon", "Noxious Shot"],
      awakened: true,
      value: "3M",
    },

    // ÉPÉES MYTHIQUES
    {
      id: "cursed-dual-katana",
      name: "Cursed Dual Katana",
      type: "sword",
      rarity: "Mythical",
      imageUrl: "/placeholder.svg?height=100&width=100",
      obtainment: "Complete Tushita and Yama puzzle",
      description: "The strongest sword in the game",
      stats: ["Highest Sword Damage", "Special Abilities", "Dual Wield", "Cursed Power"],
      damage: 95,
      mastery: 600,
      special: ["Cursed Dual Slash", "Yama", "Tushita"],
      value: "Unobtainable",
    },
    {
      id: "true-triple-katana",
      name: "True Triple Katana",
      type: "sword",
      rarity: "Mythical",
      imageUrl: "/placeholder.svg?height=100&width=100",
      obtainment: "Upgrade Triple Katana",
      description: "Enhanced version of Triple Katana",
      stats: ["Very High Damage", "Triple Slash", "Enhanced Range", "Special Moves"],
      damage: 88,
      mastery: 500,
      special: ["True Thousand Cuts", "Rengoku", "Dragon Talon"],
      value: "Upgrade",
    },
    {
      id: "dark-blade",
      name: "Dark Blade",
      type: "sword",
      rarity: "Legendary",
      imageUrl: "/placeholder.svg?height=100&width=100",
      obtainment: "Admin Item",
      description: "Exclusive admin sword with unique abilities",
      stats: ["Very High Damage", "Dark Powers", "Special Effects", "Admin Only"],
      damage: 92,
      mastery: 550,
      special: ["Dark Slash", "Shadow Strike", "Void Cut"],
      value: "Admin",
    },

    // ACCESSOIRES LÉGENDAIRES
    {
      id: "pale-scarf",
      name: "Pale Scarf",
      type: "accessory",
      rarity: "Legendary",
      imageUrl: "/placeholder.svg?height=100&width=100",
      obtainment: "Don Swan Boss (5% drop)",
      description: "Increases damage and provides special abilities",
      stats: ["+7.5% Damage", "+7.5% Defense", "Special Ability", "Rare Drop"],
      value: "Boss Drop",
    },
    {
      id: "swan-glasses",
      name: "Swan Glasses",
      type: "accessory",
      rarity: "Legendary",
      imageUrl: "/placeholder.svg?height=100&width=100",
      obtainment: "Don Swan Boss (10% drop)",
      description: "Stylish glasses that boost combat effectiveness",
      stats: ["+12.5% Damage", "Enhanced Vision", "Critical Boost"],
      value: "Boss Drop",
    },
    {
      id: "leviathan-crown",
      name: "Leviathan Crown",
      type: "accessory",
      rarity: "Mythical",
      imageUrl: "/placeholder.svg?height=100&width=100",
      obtainment: "Sea Beast Hunting",
      description: "Crown of the sea beast king",
      stats: ["+15% Damage", "+10% Defense", "Water Immunity", "Sea Beast Bonus"],
      value: "Sea Beast",
    },

    // ARMES À FEU
    {
      id: "soul-guitar",
      name: "Soul Guitar",
      type: "gun",
      rarity: "Mythical",
      imageUrl: "/placeholder.svg?height=100&width=100",
      obtainment: "Complete Soul Guitar Quest",
      description: "Musical weapon with soul power",
      stats: ["High Damage", "Soul Power", "Musical Attacks", "Special Effects"],
      damage: 75,
      mastery: 400,
      special: ["Haunting Melody", "Soul Shred", "Spectral Symphony"],
      value: "Quest",
    },
    {
      id: "acidum-rifle",
      name: "Acidum Rifle",
      type: "gun",
      rarity: "Legendary",
      imageUrl: "/placeholder.svg?height=100&width=100",
      obtainment: "Factory Raid",
      description: "Acid-powered rifle with corrosive shots",
      stats: ["High Damage", "Acid DOT", "Long Range", "Corrosive Effect"],
      damage: 70,
      mastery: 350,
      special: ["Acid Shot", "Corrosive Blast", "Toxic Rain"],
      value: "Raid",
    },

    // MATÉRIAUX
    {
      id: "dragon-scale",
      name: "Dragon Scale",
      type: "material",
      rarity: "Legendary",
      imageUrl: "/placeholder.svg?height=100&width=100",
      obtainment: "Dragon Raid",
      description: "Rare material from dragon raids",
      stats: ["Crafting Material", "Dragon Power", "Rare Drop"],
      value: "Raid Drop",
    },
    {
      id: "mystic-droplet",
      name: "Mystic Droplet",
      type: "material",
      rarity: "Mythical",
      imageUrl: "/placeholder.svg?height=100&width=100",
      obtainment: "Sea Beast Hunting",
      description: "Mystical essence from sea beasts",
      stats: ["Crafting Material", "Mystic Power", "Very Rare"],
      value: "Sea Beast",
    },
  ]

  const filteredItems = useMemo(() => {
    const filtered = databaseItems.filter((item) => {
      const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesType = selectedType === "all" || item.type === selectedType
      const matchesRarity = selectedRarity === "all" || item.rarity === selectedRarity
      return matchesSearch && matchesType && matchesRarity
    })

    // Tri
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name)
        case "rarity":
          const rarityOrder = { Common: 1, Uncommon: 2, Rare: 3, Legendary: 4, Mythical: 5 }
          return rarityOrder[b.rarity] - rarityOrder[a.rarity]
        case "damage":
          return (b.damage || 0) - (a.damage || 0)
        case "type":
          return a.type.localeCompare(b.type)
        default:
          return 0
      }
    })

    return filtered
  }, [databaseItems, searchTerm, selectedType, selectedRarity, sortBy])

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case "Common":
        return "bg-gray-500/20 text-gray-400 border-gray-500/50"
      case "Uncommon":
        return "bg-green-500/20 text-green-400 border-green-500/50"
      case "Rare":
        return "bg-blue-500/20 text-blue-400 border-blue-500/50"
      case "Legendary":
        return "bg-purple-500/20 text-purple-400 border-purple-500/50"
      case "Mythical":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/50"
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/50"
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "fruit":
        return "🍎"
      case "sword":
        return "⚔️"
      case "accessory":
        return "👑"
      case "gun":
        return "🔫"
      case "material":
        return "💎"
      default:
        return "📦"
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "fruit":
        return "text-red-400"
      case "sword":
        return "text-blue-400"
      case "accessory":
        return "text-purple-400"
      case "gun":
        return "text-orange-400"
      case "material":
        return "text-green-400"
      default:
        return "text-gray-400"
    }
  }

  // Statistiques rapides
  const stats = useMemo(() => {
    return {
      total: databaseItems.length,
      fruits: databaseItems.filter((i) => i.type === "fruit").length,
      swords: databaseItems.filter((i) => i.type === "sword").length,
      accessories: databaseItems.filter((i) => i.type === "accessory").length,
      guns: databaseItems.filter((i) => i.type === "gun").length,
      materials: databaseItems.filter((i) => i.type === "material").length,
      mythical: databaseItems.filter((i) => i.rarity === "Mythical").length,
      legendary: databaseItems.filter((i) => i.rarity === "Legendary").length,
    }
  }, [databaseItems])

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="font-gaming text-3xl font-bold mb-2 flex items-center gap-2">
          <Database className="w-8 h-8 text-primary" />
          Items Database
        </h1>
        <p className="text-muted-foreground">
          Complete catalog of all Blox Fruits items with detailed stats and information
        </p>
      </div>

      {/* Category Navigation */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
        <Card className="gaming-card group hover:scale-105 transition-all duration-300">
          <Link href="/database/fruit">
            <CardContent className="p-6 text-center">
              <div className="text-4xl mb-2">🍎</div>
              <div className="font-semibold text-red-400">Devil Fruits</div>
              <div className="text-sm text-muted-foreground">{stats.fruits} items</div>
              <ArrowRight className="w-4 h-4 mx-auto mt-2 opacity-0 group-hover:opacity-100 transition-opacity" />
            </CardContent>
          </Link>
        </Card>

        <Card className="gaming-card group hover:scale-105 transition-all duration-300">
          <Link href="/database/sword">
            <CardContent className="p-6 text-center">
              <div className="text-4xl mb-2">⚔️</div>
              <div className="font-semibold text-blue-400">Swords</div>
              <div className="text-sm text-muted-foreground">{stats.swords} items</div>
              <ArrowRight className="w-4 h-4 mx-auto mt-2 opacity-0 group-hover:opacity-100 transition-opacity" />
            </CardContent>
          </Link>
        </Card>

        <Card className="gaming-card group hover:scale-105 transition-all duration-300">
          <Link href="/database/accessory">
            <CardContent className="p-6 text-center">
              <div className="text-4xl mb-2">👑</div>
              <div className="font-semibold text-purple-400">Accessories</div>
              <div className="text-sm text-muted-foreground">{stats.accessories} items</div>
              <ArrowRight className="w-4 h-4 mx-auto mt-2 opacity-0 group-hover:opacity-100 transition-opacity" />
            </CardContent>
          </Link>
        </Card>

        <Card className="gaming-card group hover:scale-105 transition-all duration-300">
          <Link href="/database/gun">
            <CardContent className="p-6 text-center">
              <div className="text-4xl mb-2">🔫</div>
              <div className="font-semibold text-orange-400">Guns</div>
              <div className="text-sm text-muted-foreground">{stats.guns} items</div>
              <ArrowRight className="w-4 h-4 mx-auto mt-2 opacity-0 group-hover:opacity-100 transition-opacity" />
            </CardContent>
          </Link>
        </Card>

        <Card className="gaming-card group hover:scale-105 transition-all duration-300">
          <Link href="/database/material">
            <CardContent className="p-6 text-center">
              <div className="text-4xl mb-2">💎</div>
              <div className="font-semibold text-green-400">Materials</div>
              <div className="text-sm text-muted-foreground">{stats.materials} items</div>
              <ArrowRight className="w-4 h-4 mx-auto mt-2 opacity-0 group-hover:opacity-100 transition-opacity" />
            </CardContent>
          </Link>
        </Card>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 mb-6">
        <Card className="gaming-card">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{stats.total}</div>
            <div className="text-xs text-muted-foreground">Total Items</div>
          </CardContent>
        </Card>
        <Card className="gaming-card">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-400">{stats.fruits}</div>
            <div className="text-xs text-muted-foreground">Devil Fruits</div>
          </CardContent>
        </Card>
        <Card className="gaming-card">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-400">{stats.swords}</div>
            <div className="text-xs text-muted-foreground">Swords</div>
          </CardContent>
        </Card>
        <Card className="gaming-card">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-400">{stats.accessories}</div>
            <div className="text-xs text-muted-foreground">Accessories</div>
          </CardContent>
        </Card>
        <Card className="gaming-card">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-400">{stats.guns}</div>
            <div className="text-xs text-muted-foreground">Guns</div>
          </CardContent>
        </Card>
        <Card className="gaming-card">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-400">{stats.materials}</div>
            <div className="text-xs text-muted-foreground">Materials</div>
          </CardContent>
        </Card>
        <Card className="gaming-card">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-400">{stats.mythical}</div>
            <div className="text-xs text-muted-foreground">Mythical</div>
          </CardContent>
        </Card>
        <Card className="gaming-card">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-400">{stats.legendary}</div>
            <div className="text-xs text-muted-foreground">Legendary</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="gaming-card mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            Search & Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="fruit">🍎 Devil Fruits</SelectItem>
                <SelectItem value="sword">⚔️ Swords</SelectItem>
                <SelectItem value="accessory">👑 Accessories</SelectItem>
                <SelectItem value="gun">🔫 Guns</SelectItem>
                <SelectItem value="material">💎 Materials</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedRarity} onValueChange={setSelectedRarity}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by rarity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Rarities</SelectItem>
                <SelectItem value="Common">Common</SelectItem>
                <SelectItem value="Uncommon">Uncommon</SelectItem>
                <SelectItem value="Rare">Rare</SelectItem>
                <SelectItem value="Legendary">Legendary</SelectItem>
                <SelectItem value="Mythical">Mythical</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="rarity">Rarity</SelectItem>
                <SelectItem value="damage">Damage</SelectItem>
                <SelectItem value="type">Type</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredItems.map((item) => (
          <Card
            key={item.id}
            className="gaming-card group hover:scale-105 transition-all duration-300 hover:shadow-lg hover:shadow-primary/20"
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-2xl">{getTypeIcon(item.type)}</span>
                  <div>
                    <CardTitle className="text-lg flex items-center gap-2">
                      {item.name}
                      {item.awakened && <Zap className="w-4 h-4 text-yellow-400" />}
                      {item.damage && item.damage >= 90 && <Star className="w-4 h-4 text-yellow-400" />}
                    </CardTitle>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge className={`text-xs ${getRarityColor(item.rarity)}`}>{item.rarity}</Badge>
                      <span className={`text-xs ${getTypeColor(item.type)} capitalize`}>{item.type}</span>
                    </div>
                  </div>
                </div>
                <Button variant="ghost" size="sm" asChild>
                  <Link href={`/database/${item.type}/${item.id}`}>
                    <ExternalLink className="w-4 h-4" />
                  </Link>
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="aspect-square bg-secondary/20 rounded-lg flex items-center justify-center overflow-hidden">
                <img
                  src={item.imageUrl || "/placeholder.svg"}
                  alt={item.name}
                  className="w-full h-full object-cover rounded-lg transition-transform group-hover:scale-110"
                  onError={(e) => {
                    e.currentTarget.src = "/placeholder.svg?height=100&width=100"
                  }}
                />
              </div>

              {item.description && <p className="text-sm text-muted-foreground line-clamp-2">{item.description}</p>}

              {/* Damage et Mastery */}
              {(item.damage || item.mastery) && (
                <div className="flex justify-between text-sm">
                  {item.damage && (
                    <div className="flex items-center gap-1">
                      <Shield className="w-3 h-3 text-red-400" />
                      <span className="text-red-400 font-mono">{item.damage}</span>
                    </div>
                  )}
                  {item.mastery && (
                    <div className="flex items-center gap-1">
                      <TrendingUp className="w-3 h-3 text-blue-400" />
                      <span className="text-blue-400 font-mono">{item.mastery}</span>
                    </div>
                  )}
                </div>
              )}

              {/* Stats */}
              {item.stats && (
                <div className="space-y-1">
                  <h4 className="text-sm font-semibold">Stats:</h4>
                  <div className="space-y-1">
                    {item.stats.slice(0, 3).map((stat, index) => (
                      <div key={index} className="text-xs bg-secondary/20 px-2 py-1 rounded flex items-center gap-1">
                        <div className="w-1 h-1 bg-primary rounded-full"></div>
                        {stat}
                      </div>
                    ))}
                    {item.stats.length > 3 && (
                      <div className="text-xs text-muted-foreground">+{item.stats.length - 3} more...</div>
                    )}
                  </div>
                </div>
              )}

              {/* Special Moves */}
              {item.special && (
                <div className="space-y-1">
                  <h4 className="text-sm font-semibold text-primary">Special Moves:</h4>
                  <div className="space-y-1">
                    {item.special.slice(0, 2).map((move, index) => (
                      <div key={index} className="text-xs bg-primary/10 px-2 py-1 rounded text-primary">
                        {move}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="pt-2 border-t border-border/50">
                {item.price && (
                  <div className="flex justify-between text-sm">
                    <span>Price:</span>
                    <span className="font-mono text-primary">{item.price}</span>
                  </div>
                )}
                {item.robuxPrice && (
                  <div className="flex justify-between text-sm">
                    <span>Robux:</span>
                    <span className="font-mono text-yellow-400">{item.robuxPrice}</span>
                  </div>
                )}
                {item.value && (
                  <div className="flex justify-between text-sm">
                    <span>Value:</span>
                    <span className="font-mono text-green-400">{item.value}</span>
                  </div>
                )}
                {item.obtainment && (
                  <div className="text-xs text-muted-foreground mt-1">
                    <strong>How to get:</strong> {item.obtainment}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredItems.length === 0 && (
        <Card className="gaming-card">
          <CardContent className="text-center py-12">
            <Database className="w-16 h-16 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-semibold mb-2">No items found</h3>
            <p className="text-muted-foreground">Try adjusting your search terms or filters</p>
            <Button
              variant="outline"
              className="mt-4 bg-transparent"
              onClick={() => {
                setSearchTerm("")
                setSelectedType("all")
                setSelectedRarity("all")
              }}
            >
              Clear Filters
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
