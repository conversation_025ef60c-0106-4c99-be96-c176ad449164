import { MongoClient } from "mongodb"

export interface ScrapingStats {
  totalItems: number
  byCategory: Record<string, number>
  dataQuality: Record<string, number>
  errors: string[]
  duration: number
  timestamp: Date
  version: string
}

export interface ScraperStatus {
  isRunning: boolean
  lastRun?: Date
  nextRun?: Date
  totalItems: number
  byCategory: Record<string, number>
  dataQuality: Record<string, number>
  errors: string[]
  version: string
}

export class ScraperStatusManager {
  private mongoClient: MongoClient

  constructor(mongoUrl: string) {
    this.mongoClient = new MongoClient(mongoUrl)
  }

  async getStatus(): Promise<ScraperStatus> {
    try {
      await this.mongoClient.connect()
      const db = this.mongoClient.db("bloxfruits")

      // Vérifier si un scraping est en cours (via un lock file ou un timestamp récent)
      const lockCollection = db.collection("scraper_locks")
      const activeLock = await lockCollection.findOne({ active: true })

      // Récupérer les dernières statistiques
      const statsCollection = db.collection("scraping_stats")
      const lastStats = await statsCollection.findOne<ScrapingStats>({}, { sort: { timestamp: -1 } })

      // Compter les items dans chaque collection
      const collections = [
        "fruits",
        "swords",
        "accessories",
        "guns",
        "materials",
        "npcs",
        "quests",
        "raids",
        "mechanics",
      ] // Added mechanics
      const byCategory: Record<string, number> = {}
      let totalItems = 0

      for (const collectionName of collections) {
        const count = await db.collection(collectionName).countDocuments()
        byCategory[collectionName] = count
        totalItems += count
      }

      const status: ScraperStatus = {
        isRunning: !!activeLock,
        lastRun: lastStats?.timestamp,
        nextRun: this.calculateNextRun(),
        totalItems,
        byCategory,
        dataQuality: lastStats?.dataQuality || {},
        errors: lastStats?.errors || [],
        version: lastStats?.version || "1.0",
      }

      return status
    } catch (error) {
      console.error("Error getting scraper status:", error)
      throw error
    } finally {
      await this.mongoClient.close()
    }
  }

  async setRunning(isRunning: boolean): Promise<void> {
    try {
      await this.mongoClient.connect()
      const db = this.mongoClient.db("bloxfruits")
      const lockCollection = db.collection("scraper_locks")

      if (isRunning) {
        await lockCollection.replaceOne(
          { _id: "main" },
          {
            _id: "main",
            active: true,
            startedAt: new Date(),
            pid: process.pid,
          },
          { upsert: true },
        )
      } else {
        await lockCollection.deleteOne({ _id: "main" })
      }
    } catch (error) {
      console.error("Error setting scraper status:", error)
      throw error
    } finally {
      await this.mongoClient.close()
    }
  }

  async saveStats(stats: any): Promise<void> {
    try {
      await this.mongoClient.connect()
      const db = this.mongoClient.db("bloxfruits")
      const statsCollection = db.collection("scraping_stats")

      await statsCollection.insertOne({
        ...stats,
        timestamp: new Date(),
      })

      // Garder seulement les 100 dernières entrées
      const totalCount = await statsCollection.countDocuments()
      if (totalCount > 100) {
        const oldestStats = await statsCollection
          .find({})
          .sort({ timestamp: 1 })
          .limit(totalCount - 100)
          .toArray()

        const idsToDelete = oldestStats.map((stat) => stat._id)
        await statsCollection.deleteMany({ _id: { $in: idsToDelete } })
      }
    } catch (error) {
      console.error("Error saving scraper stats:", error)
      throw error
    } finally {
      await this.mongoClient.close()
    }
  }

  async getHistory(limit = 10): Promise<any[]> {
    try {
      await this.mongoClient.connect()
      const db = this.mongoClient.db("bloxfruits")
      const statsCollection = db.collection("scraping_stats")

      return await statsCollection.find({}).sort({ timestamp: -1 }).limit(limit).toArray()
    } catch (error) {
      console.error("Error getting scraper history:", error)
      throw error
    } finally {
      await this.mongoClient.close()
    }
  }

  private calculateNextRun(): Date {
    // Calculer la prochaine exécution (toutes les heures)
    const now = new Date()
    const nextHour = new Date(now)
    nextHour.setHours(now.getHours() + 1, 0, 0, 0)
    return nextHour
  }

  async getDataQualityReport(): Promise<any> {
    try {
      await this.mongoClient.connect()
      const db = this.mongoClient.db("bloxfruits")

      const collections = [
        "fruits",
        "swords",
        "accessories",
        "guns",
        "materials",
        "npcs",
        "quests",
        "raids",
        "mechanics",
      ] // Added mechanics
      const report: any = {
        totalItems: 0,
        qualityByCollection: {},
        missingDataSummary: {},
        lastUpdated: new Date(),
      }

      for (const collectionName of collections) {
        const collection = db.collection(collectionName)
        const items = await collection.find({}).toArray()

        if (items.length === 0) {
          report.qualityByCollection[collectionName] = {
            count: 0,
            quality: 0,
            missingFields: [],
          }
          continue
        }

        let totalQuality = 0
        const missingFields: Record<string, number> = {}

        items.forEach((item) => {
          let itemQuality = 0
          const requiredFields = ["name", "type", "category"]
          const optionalFields = ["rarity", "price", "description", "moves", "stats"]

          // Vérifier les champs requis
          requiredFields.forEach((field) => {
            if (item[field] && item[field] !== "Unknown") {
              itemQuality += 20
            } else {
              missingFields[field] = (missingFields[field] || 0) + 1
            }
          })

          // Vérifier les champs optionnels
          optionalFields.forEach((field) => {
            if (item[field]) {
              itemQuality += 8
            } else {
              missingFields[field] = (missingFields[field] || 0) + 1
            }
          })

          totalQuality += itemQuality
        })

        report.qualityByCollection[collectionName] = {
          count: items.length,
          quality: Math.round(totalQuality / items.length),
          missingFields: Object.entries(missingFields)
            .sort(([, a], [, b]) => (b as number) - (a as number))
            .slice(0, 5),
        }

        report.totalItems += items.length
      }

      return report
    } catch (error) {
      console.error("Error generating data quality report:", error)
      throw error
    } finally {
      await this.mongoClient.close()
    }
  }
}

export async function getLatestScrapingStatus(mongoUrl: string): Promise<ScrapingStats | null> {
  const client = new MongoClient(mongoUrl)
  try {
    await client.connect()
    const db = client.db("bloxfruits")
    const collection = db.collection("scraping_stats")

    const latestStats = await collection.findOne<ScrapingStats>(
      {},
      { sort: { timestamp: -1 } }, // Get the most recent one
    )
    return latestStats
  } catch (error) {
    console.error("Error fetching latest scraping status:", error)
    return null
  } finally {
    await client.close()
  }
}

export async function getScrapingHistory(mongoUrl: string, limit = 10): Promise<ScrapingStats[]> {
  const client = new MongoClient(mongoUrl)
  try {
    await client.connect()
    const db = client.db("bloxfruits")
    const collection = db.collection("scraping_stats")

    const history = (await collection.find({}).sort({ timestamp: -1 }).limit(limit).toArray()) as ScrapingStats[]
    return history
  } catch (error) {
    console.error("Error fetching scraping history:", error)
    return []
  } finally {
    await client.close()
  }
}

export async function getScraperStatus(mongoUrl: string) {
  const client = new MongoClient(mongoUrl)
  try {
    await client.connect()
    const db = client.db("bloxfruits")
    const collection = db.collection("scraping_stats")

    const latestStat = await collection.findOne({}, { sort: { timestamp: -1 } })

    if (latestStat) {
      return {
        lastRun: latestStat.timestamp,
        totalItems: latestStat.totalItems,
        byCategory: latestStat.byCategory,
        averageQuality: latestStat.dataQuality
          ? Math.round(
              Object.values(latestStat.dataQuality).reduce((a: number, b: number) => a + b, 0) /
                Object.values(latestStat.dataQuality).length,
            )
          : 0,
        errors: latestStat.errors || [],
        duration: latestStat.duration,
        version: latestStat.version || "N/A",
      }
    } else {
      return {
        lastRun: null,
        totalItems: 0,
        byCategory: {},
        averageQuality: 0,
        errors: ["No scraping data found yet."],
        duration: 0,
        version: "N/A",
      }
    }
  } catch (error) {
    console.error("Failed to get scraper status:", error)
    return {
      lastRun: null,
      totalItems: 0,
      byCategory: {},
      averageQuality: 0,
      errors: [`Failed to connect to DB or fetch status: ${error}`],
      duration: 0,
      version: "N/A",
    }
  } finally {
    await client.close()
  }
}
