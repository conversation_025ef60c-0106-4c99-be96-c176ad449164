import { BaseScraper } from "../base-scraper"
import { ScrapedItem, NPCData } from "../types"

export class NPC<PERSON><PERSON>raper extends BaseScraper {
  
  extractSpecificData(wikitext: string, infoboxData: Record<string, string>): NPCData {
    const npcData: any = {}

    // Extract NPC type
    if (infoboxData.type) {
      const type = infoboxData.type.trim()
      if (["Quest", "Shop", "Misc", "Boss", "Enemy"].includes(type)) {
        npcData.npcType = type as "Quest" | "Shop" | "Misc" | "Boss" | "Enemy"
      }
    }

    // Extract sea and location
    if (infoboxData.sea) {
      const sea = this.parseNumber(infoboxData.sea)
      if (sea) npcData.sea = sea
    }

    // Extract locations (can be multiple)
    const locations: string[] = []
    if (infoboxData.location) locations.push(infoboxData.location)
    if (infoboxData.location1) locations.push(infoboxData.location1)
    if (infoboxData.location2) locations.push(infoboxData.location2)
    if (infoboxData.location3) locations.push(infoboxData.location3)

    if (locations.length > 0) {
      npcData.locations = locations
      npcData.location = locations[0] // Keep compatibility
    }

    // Extract services offered
    const services: string[] = []

    // Detect services from wikitext
    if (wikitext.includes("change the appearance") || wikitext.includes("Editor")) {
      services.push("Appearance Editor")
    }
    if (wikitext.includes("Quest Giver") || wikitext.includes("quest")) {
      services.push("Quest Giver")
    }
    if (wikitext.includes("Dealer") || wikitext.includes("shop") || wikitext.includes("buy")) {
      services.push("Shop")
    }
    if (wikitext.includes("upgrade") || wikitext.includes("enhance")) {
      services.push("Upgrader")
    }
    if (wikitext.toLowerCase().includes('evolve') || wikitext.toLowerCase().includes('upgrade')) {
      services.push("Race Evolution")
    }

    if (services.length > 0) {
      npcData.services = [...new Set(services)] // Remove duplicates
    }

    // Extract quest requirements
    const requirementsMatch = wikitext.match(/==\s*Requirements\s*==([\s\S]*?)(?===|$)/i)
    if (requirementsMatch) {
      const requirements: Array<{ type: string; description: string; amount?: number }> = []
      const reqContent = requirementsMatch[1]

      // Look for requirement patterns
      const reqLines = reqContent.split('\n').filter(line => line.trim().startsWith('*'))

      reqLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 5) {
          // Extract monetary amounts
          const moneyMatch = cleanLine.match(/\{\{Money\|([^}]+)\}\}/)
          if (moneyMatch) {
            const amount = this.parseNumber(moneyMatch[1])
            requirements.push({
              type: "Money",
              description: cleanLine,
              amount: amount
            })
          } else {
            requirements.push({
              type: "General",
              description: cleanLine
            })
          }
        }
      })

      if (requirements.length > 0) {
        npcData.questRequirements = requirements
      }
    }

    // Extract quest steps from tables
    const questTableMatch = wikitext.match(/\{\|\s*class="fandom-table"[\s\S]*?\|\}/i)
    if (questTableMatch) {
      const questSteps: string[] = []
      const tableContent = questTableMatch[0]

      // Extract table rows
      const rows = tableContent.split('|-').slice(1)

      rows.forEach(row => {
        const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell && !cell.startsWith('{'))
        if (cells.length >= 2) {
          const race = this.cleanWikitext(cells[0])
          const quest = this.cleanWikitext(cells[1])
          if (race && quest && quest.length > 10) {
            questSteps.push(`${race}: ${quest}`)
          }
        }
      })

      if (questSteps.length > 0) {
        npcData.questSteps = questSteps
      }
    }

    // Extract dialogue
    const dialogue: string[] = []
    const dialoguePatterns = [
      /"([^"]+)"/g,
      /''([^']+)''/g
    ]

    dialoguePatterns.forEach(pattern => {
      let match
      while ((match = pattern.exec(wikitext)) !== null) {
        const dialogueLine = this.cleanWikitext(match[1])
        if (dialogueLine.length > 5 && dialogueLine.length < 200) {
          dialogue.push(dialogueLine)
        }
      }
    })

    if (dialogue.length > 0) {
      npcData.dialogue = [...new Set(dialogue)].slice(0, 10) // Remove duplicates, limit to 10
    }

    // Extract cost information
    if (infoboxData.cost) {
      const cost = this.parseNumber(infoboxData.cost)
      if (cost) npcData.cost = cost
    }

    // Extract quest rewards
    const rewardsMatch = wikitext.match(/==\s*Rewards?\s*==([\s\S]*?)(?===|$)/i)
    if (rewardsMatch) {
      const rewards: Array<{ type: string; description: string; amount?: number }> = []
      const rewardContent = rewardsMatch[1]

      const rewardLines = rewardContent.split('\n').filter(line => line.trim().startsWith('*'))

      rewardLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 5) {
          // Extract monetary rewards
          const moneyMatch = cleanLine.match(/\{\{Money\|([^}]+)\}\}/)
          if (moneyMatch) {
            const amount = this.parseNumber(moneyMatch[1])
            rewards.push({
              type: "Money",
              description: cleanLine,
              amount: amount
            })
          } else if (cleanLine.toLowerCase().includes('exp')) {
            // Extract experience rewards
            const expMatch = cleanLine.match(/(\d+)\s*exp/i)
            if (expMatch) {
              rewards.push({
                type: "Experience",
                description: cleanLine,
                amount: parseInt(expMatch[1])
              })
            } else {
              rewards.push({
                type: "Experience",
                description: cleanLine
              })
            }
          } else {
            rewards.push({
              type: "General",
              description: cleanLine
            })
          }
        }
      })

      if (rewards.length > 0) {
        npcData.questRewards = rewards
      }
    }

    return npcData
  }

  async scrapeItem(title: string, itemType: string): Promise<ScrapedItem | null> {
    console.log(`👤 Scraping NPC: ${title}`)

    const wikitext = await this.getPageContent(title)
    if (!wikitext) {
      console.warn(`⚠️ No content found for ${title}`)
      return null
    }

    // Extract infobox data
    const infoboxData = this.extractTemplateData(wikitext, "Infobox")

    // Extract NPC-specific data
    const npcData = this.extractSpecificData(wikitext, infoboxData)

    // Create the scraped item
    const item: ScrapedItem = {
      name: title,
      type: itemType,
      category: "npc",
      wikiUrl: `https://blox-fruits.fandom.com/wiki/${encodeURIComponent(title)}`,
      lastUpdated: new Date(),
      npcData,
      rawData: {
        infobox: infoboxData,
        wikitextLength: wikitext.length,
        movesFound: 0,
        statsFound: 0,
        extractedAt: new Date().toISOString(),
        npcData
      }
    }

    // Extract basic properties from infobox
    if (infoboxData.description) item.description = this.cleanWikitext(infoboxData.description)
    if (npcData.location) item.location = npcData.location

    return item
  }

  async scrapeCategory(categoryName: string = "NPCs"): Promise<ScrapedItem[]> {
    console.log(`\n🎯 Starting to scrape NPCs from category: ${categoryName}`)

    const members = await this.getCategoryMembers(categoryName)
    if (members.length === 0) {
      return []
    }

    const items: ScrapedItem[] = []
    let processed = 0

    for (const member of members) {
      try {
        const item = await this.scrapeItem(member.title, "npc")
        if (item) {
          items.push(item)
        }
        processed++

        // Progress indicator
        if (processed % 5 === 0 || processed === members.length) {
          console.log(
            `📊 Progress: ${processed}/${members.length} (${Math.round((processed / members.length) * 100)}%)`
          )
        }

        // Small delay between items
        await new Promise((resolve) => setTimeout(resolve, 200))
      } catch (error) {
        console.error(`❌ Error scraping NPC ${member.title}:`, error)
      }
    }

    console.log(`✅ Completed NPCs: ${items.length}/${members.length} items scraped`)
    return items
  }
}
