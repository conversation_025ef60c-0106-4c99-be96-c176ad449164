"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AdBanner } from "@/components/ads/ad-banner"
import { Search, Calendar, User, Eye, Heart, MessageCircle, TrendingUp, BookOpen, Filter } from "lucide-react"
import Link from "next/link"

interface BlogPost {
  _id: string
  title: string
  slug: string
  excerpt: string
  author: {
    name: string
    avatar: string
  }
  category: string
  tags: string[]
  publishedAt: string
  views: number
  likes: number
  comments: number
  featured: boolean
  readTime: number
  featuredImage?: string
}

const categories = ["All", "Guides", "PvP", "Trading", "Builds", "Fruits", "Updates", "News"]

export default function BlogPage() {
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [sortBy, setSortBy] = useState("latest")

  // Mock data pour la démonstration
  const mockPosts: BlogPost[] = [
    {
      _id: "1",
      title: "Complete Guide to Blox Fruits Trading in 2024",
      slug: "complete-guide-blox-fruits-trading-2024",
      excerpt:
        "Master the art of trading in Blox Fruits with our comprehensive guide covering values, strategies, and tips.",
      author: {
        name: "BloxMaster",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      category: "Trading",
      tags: ["trading", "guide", "values", "tips"],
      publishedAt: "2024-01-15T10:00:00Z",
      views: 15420,
      likes: 892,
      comments: 156,
      featured: true,
      readTime: 8,
      featuredImage: "/placeholder.svg?height=200&width=400",
    },
    {
      _id: "2",
      title: "Best PvP Builds for Third Sea",
      slug: "best-pvp-builds-third-sea",
      excerpt:
        "Dominate PvP battles with these optimized builds for the Third Sea. Includes stat distributions and combos.",
      author: {
        name: "PvPPro",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      category: "PvP",
      tags: ["pvp", "builds", "third-sea", "combos"],
      publishedAt: "2024-01-14T15:30:00Z",
      views: 8750,
      likes: 445,
      comments: 89,
      featured: false,
      readTime: 6,
    },
    {
      _id: "3",
      title: "Kitsune Fruit: Complete Moveset Analysis",
      slug: "kitsune-fruit-complete-moveset-analysis",
      excerpt: "Deep dive into the Kitsune fruit's abilities, damage output, and optimal usage strategies.",
      author: {
        name: "FruitExpert",
        avatar: "/placeholder.svg?height=40&width=40",
      },
      category: "Fruits",
      tags: ["kitsune", "fruit", "moveset", "analysis"],
      publishedAt: "2024-01-13T12:00:00Z",
      views: 12300,
      likes: 678,
      comments: 234,
      featured: true,
      readTime: 10,
    },
  ]

  useEffect(() => {
    // Simuler le chargement des posts
    setIsLoading(true)
    setTimeout(() => {
      setPosts(mockPosts)
      setIsLoading(false)
    }, 1000)
  }, [])

  const filteredPosts = posts.filter((post) => {
    const matchesSearch =
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "All" || post.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const sortedPosts = [...filteredPosts].sort((a, b) => {
    switch (sortBy) {
      case "popular":
        return b.views - a.views
      case "liked":
        return b.likes - a.likes
      case "commented":
        return b.comments - a.comments
      default:
        return new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime()
    }
  })

  const featuredPosts = posts.filter((post) => post.featured)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="font-gaming text-4xl font-bold mb-4 text-white flex items-center gap-3">
            <BookOpen className="w-10 h-10 text-primary" />
            Blox Fruits Blog
          </h1>
          <p className="text-gray-300 text-lg">Latest guides, strategies, and updates for Blox Fruits</p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card className="bg-gradient-to-r from-blue-600 to-blue-700 border-blue-500">
            <CardContent className="p-4 text-center">
              <BookOpen className="w-8 h-8 mx-auto mb-2 text-white" />
              <div className="text-2xl font-bold text-white">{posts.length}</div>
              <div className="text-blue-100 text-sm">Articles</div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-green-600 to-green-700 border-green-500">
            <CardContent className="p-4 text-center">
              <Eye className="w-8 h-8 mx-auto mb-2 text-white" />
              <div className="text-2xl font-bold text-white">
                {posts.reduce((sum, post) => sum + post.views, 0).toLocaleString()}
              </div>
              <div className="text-green-100 text-sm">Total Views</div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-red-600 to-red-700 border-red-500">
            <CardContent className="p-4 text-center">
              <Heart className="w-8 h-8 mx-auto mb-2 text-white" />
              <div className="text-2xl font-bold text-white">
                {posts.reduce((sum, post) => sum + post.likes, 0).toLocaleString()}
              </div>
              <div className="text-red-100 text-sm">Total Likes</div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-purple-600 to-purple-700 border-purple-500">
            <CardContent className="p-4 text-center">
              <MessageCircle className="w-8 h-8 mx-auto mb-2 text-white" />
              <div className="text-2xl font-bold text-white">
                {posts.reduce((sum, post) => sum + post.comments, 0).toLocaleString()}
              </div>
              <div className="text-purple-100 text-sm">Comments</div>
            </CardContent>
          </Card>
        </div>

        {/* Featured Posts */}
        {featuredPosts.length > 0 && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-white mb-4 flex items-center gap-2">
              <TrendingUp className="w-6 h-6 text-primary" />
              Featured Articles
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {featuredPosts.slice(0, 2).map((post) => (
                <Card
                  key={post._id}
                  className="bg-slate-800/50 border-slate-700 hover:border-primary/50 transition-colors"
                >
                  <CardContent className="p-0">
                    {post.featuredImage && (
                      <div className="h-48 bg-gradient-to-r from-primary/20 to-purple-600/20 rounded-t-lg"></div>
                    )}
                    <div className="p-6">
                      <div className="flex items-center gap-2 mb-3">
                        <Badge className="bg-primary/20 text-primary border-primary/30">Featured</Badge>
                        <Badge variant="outline" className="border-slate-600 text-gray-300">
                          {post.category}
                        </Badge>
                      </div>
                      <h3 className="text-xl font-bold text-white mb-2 hover:text-primary transition-colors">
                        <Link href={`/blog/${post.slug}`}>{post.title}</Link>
                      </h3>
                      <p className="text-gray-300 mb-4">{post.excerpt}</p>
                      <div className="flex items-center justify-between text-sm text-gray-400">
                        <div className="flex items-center gap-4">
                          <div className="flex items-center gap-1">
                            <User className="w-3 h-3" />
                            {post.author.name}
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            {new Date(post.publishedAt).toLocaleDateString()}
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <span className="flex items-center gap-1">
                            <Eye className="w-3 h-3" />
                            {post.views.toLocaleString()}
                          </span>
                          <span className="flex items-center gap-1">
                            <Heart className="w-3 h-3" />
                            {post.likes}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Ad Banner */}
        <div className="mb-8">
          <AdBanner adSlot="blog-main" adFormat="horizontal" />
        </div>

        {/* Filters and Search */}
        <Card className="mb-8 bg-slate-800/50 border-slate-700">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search articles..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white"
                />
              </div>
              <div className="flex gap-2">
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-40 bg-slate-700 border-slate-600 text-white">
                    <Filter className="w-4 h-4 mr-2" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-40 bg-slate-700 border-slate-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="latest">Latest</SelectItem>
                    <SelectItem value="popular">Most Popular</SelectItem>
                    <SelectItem value="liked">Most Liked</SelectItem>
                    <SelectItem value="commented">Most Commented</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Articles Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="bg-slate-800/50 border-slate-700">
                <CardContent className="p-6">
                  <div className="animate-pulse">
                    <div className="h-4 bg-slate-600 rounded mb-4"></div>
                    <div className="h-6 bg-slate-600 rounded mb-2"></div>
                    <div className="h-4 bg-slate-600 rounded mb-4"></div>
                    <div className="flex justify-between">
                      <div className="h-3 bg-slate-600 rounded w-20"></div>
                      <div className="h-3 bg-slate-600 rounded w-16"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : sortedPosts.length === 0 ? (
          <Card className="bg-slate-800/50 border-slate-700 text-center py-12">
            <CardContent>
              <BookOpen className="w-16 h-16 mx-auto mb-4 text-gray-400" />
              <h3 className="text-xl font-semibold text-white mb-2">No articles found</h3>
              <p className="text-gray-400">Try adjusting your search or filter criteria</p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sortedPosts.map((post) => (
              <Card
                key={post._id}
                className="bg-slate-800/50 border-slate-700 hover:border-primary/50 transition-colors group"
              >
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-3">
                    <Badge variant="outline" className="border-slate-600 text-gray-300">
                      {post.category}
                    </Badge>
                    <span className="text-xs text-gray-400">{post.readTime} min read</span>
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2 group-hover:text-primary transition-colors">
                    <Link href={`/blog/${post.slug}`}>{post.title}</Link>
                  </h3>
                  <p className="text-gray-300 mb-4 text-sm">{post.excerpt}</p>
                  <div className="flex items-center gap-2 mb-4">
                    {post.tags.slice(0, 3).map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs bg-slate-700 text-gray-300">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  <div className="flex items-center justify-between text-xs text-gray-400">
                    <div className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      {post.author.name}
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="flex items-center gap-1">
                        <Eye className="w-3 h-3" />
                        {post.views.toLocaleString()}
                      </span>
                      <span className="flex items-center gap-1">
                        <Heart className="w-3 h-3" />
                        {post.likes}
                      </span>
                      <span className="flex items-center gap-1">
                        <MessageCircle className="w-3 h-3" />
                        {post.comments}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Load More Button */}
        {sortedPosts.length > 0 && (
          <div className="text-center mt-8">
            <Button variant="outline" className="border-slate-600 text-gray-300 hover:bg-slate-700 bg-transparent">
              Load More Articles
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
