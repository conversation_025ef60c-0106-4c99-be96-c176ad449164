import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter, Orbitron, Fira_Code } from "next/font/google"
import "./globals.css"
import { Header } from "@/components/layout/header"
import { Footer } from "@/components/layout/footer"
import { Providers } from "@/components/providers"
import Script from "next/script"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
})

const orbitron = Orbitron({
  subsets: ["latin"],
  variable: "--font-orbitron",
})

const firaCode = Fira_Code({
  subsets: ["latin"],
  variable: "--font-fira-code",
})

export const metadata: Metadata = {
  title: "Blox Fruits Calculator - Stats, Damage, Trading & More",
  description:
    "Complete calculator suite for Blox Fruits: optimize stats, calculate damage, find best farming spots, and track trading values.",
  keywords: "blox fruits, calculator, stats, damage, trading, farming, roblox",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <head>
        {/* Google AdSense */}
        <Script
          async
          src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${process.env.NEXT_PUBLIC_ADSENSE_CLIENT_ID}`}
          crossOrigin="anonymous"
          strategy="afterInteractive"
        />

        {/* Mediavine (si applicable) */}
        {process.env.NEXT_PUBLIC_MEDIAVINE_SITE_ID && (
          <Script async src="https://www.googletagservices.com/tag/js/gpt.js" strategy="afterInteractive" />
        )}
      </head>
      <body
        className={`${inter.variable} ${orbitron.variable} ${firaCode.variable} font-sans bg-background text-foreground antialiased`}
      >
        <Providers>
          <div className="min-h-screen flex flex-col">
            <Header />
            <main className="flex-1">{children}</main>
            <Footer />
          </div>
        </Providers>
      </body>
    </html>
  )
}
