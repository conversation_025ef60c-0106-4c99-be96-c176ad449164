import dotenv from "dotenv"
import { ScraperCoordinator } from "../lib/scrapers/scraper-coordinator"
import { MongoClient } from "mongodb"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

interface ScrapingStats {
  totalItems: number
  byCategory: Record<string, number>
  dataQuality: Record<string, number>
  errors: string[]
  duration: number
}

async function runComprehensiveScraper() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    console.log("💡 Create a .env.local file with:")
    console.log("MONGODB_URL=mongodb://localhost:27017/bloxfruits")
    console.log("or your MongoDB connection string")
    process.exit(1)
  }

  const startTime = Date.now()
  const stats: ScrapingStats = {
    totalItems: 0,
    byCategory: {},
    dataQuality: {},
    errors: [],
    duration: 0,
  }

  console.log("🚀 BLOX FRUITS COMPREHENSIVE SCRAPER v3.0")
  console.log("==========================================")
  console.log("Using the new modular architecture to scrape ALL data from Blox Fruits Wiki")
  console.log("This includes: Fruits, Materials, Weapons, Accessories, NPCs, Quests, Raids, and Mechanics")
  console.log("")

  const coordinator = new ScraperCoordinator(mongoUrl)
  const client = new MongoClient(mongoUrl)

  try {
    await client.connect()
    const db = client.db("bloxfruits")

    // Définir les catégories avec leurs collections correctes (architecture modulaire)
    const categories = [
      { name: "Blox_Fruits", type: "fruit", collection: "fruits", emoji: "🍎" },
      { name: "Materials", type: "material", collection: "materials", emoji: "🧱" },
      { name: "Swords", type: "sword", collection: "swords", emoji: "⚔️" },
      { name: "Guns", type: "gun", collection: "guns", emoji: "🔫" },
      { name: "Accessories", type: "accessory", collection: "accessories", emoji: "💍" },
      { name: "NPCs", type: "npc", collection: "npcs", emoji: "👤" },
      { name: "Quests", type: "quest", collection: "quests", emoji: "📋" },
      { name: "Raids", type: "raid", collection: "raids", emoji: "👹" },
      { name: "Game_Mechanics", type: "mechanic", collection: "mechanics", emoji: "⚙️" },
    ]

    // Scraper chaque catégorie avec la nouvelle architecture modulaire
    for (const category of categories) {
      console.log(`\n${category.emoji} Processing category: ${category.name}`)

      try {
        // Utiliser le coordinateur pour scraper la catégorie
        const items = await coordinator.scrapeCategory({
          name: category.name,
          type: category.type,
          collection: category.collection
        })

        if (items.length > 0) {
          // Les items sont déjà sauvegardés par le coordinateur
          // Calculer la qualité des données
          const qualityScore = calculateCategoryQuality(items)

          stats.byCategory[category.type] = items.length
          stats.dataQuality[category.type] = qualityScore
          stats.totalItems += items.length

          console.log(`✅ ${category.type}: ${items.length} items (Quality: ${qualityScore}%)`)

          // Afficher quelques statistiques spécifiques
          displayCategoryStats(category.type, items)
        } else {
          console.warn(`⚠️ No items found for ${category.name}`)
          stats.errors.push(`No items found for ${category.name}`)
        }

        // Pause entre catégories (le coordinateur gère déjà les pauses)
        console.log("⏸️ Pausing before next category...")
        await new Promise((resolve) => setTimeout(resolve, 3000))
      } catch (error) {
        console.error(`❌ Error processing ${category.name}:`, error)
        stats.errors.push(`${category.name}: ${error}`)
      }
    }

    stats.duration = Date.now() - startTime

    // Afficher les statistiques finales
    displayFinalStats(stats)

    // Sauvegarder les stats
    await saveScrapingStats(db, stats)

    // Envoyer notification Discord
    if (process.env.DISCORD_WEBHOOK_URL) {
      await sendDiscordNotification(stats)
    }
  } catch (error) {
    console.error("❌ Fatal error during comprehensive scraping:", error)
    stats.errors.push(`Fatal error: ${error}`)
  } finally {
    await coordinator.close()
    await client.close()
  }
}

function displayCategoryStats(categoryType: string, items: any[]) {
  if (items.length === 0) return

  const sample = items[0]
  console.log(`   📊 Sample data for ${categoryType}:`)

  switch (categoryType) {
    case 'fruit':
      const fruitsWithAwakening = items.filter(item => item.fruitData?.awakening).length
      const fruitsWithType = items.filter(item => item.fruitData?.type).length
      console.log(`      - Fruits with awakening: ${fruitsWithAwakening}/${items.length}`)
      console.log(`      - Fruits with type: ${fruitsWithType}/${items.length}`)
      break

    case 'material':
      const materialsWithLocations = items.filter(item => item.materialData?.locations?.length > 0).length
      const materialsWithUsage = items.filter(item => item.materialData?.usage?.length > 0).length
      console.log(`      - Materials with locations: ${materialsWithLocations}/${items.length}`)
      console.log(`      - Materials with usage info: ${materialsWithUsage}/${items.length}`)
      break

    case 'sword':
    case 'gun':
      const weaponsWithStats = items.filter(item => item.weaponData?.stats?.length > 0).length
      const weaponsWithPros = items.filter(item => item.weaponData?.pros?.length > 0).length
      console.log(`      - Weapons with stats: ${weaponsWithStats}/${items.length}`)
      console.log(`      - Weapons with pros/cons: ${weaponsWithPros}/${items.length}`)
      break

    case 'accessory':
      const accessoriesWithBuffs = items.filter(item => item.accessoryData?.buffs?.length > 0).length
      const accessoriesWithRarity = items.filter(item => item.accessoryData?.rarity).length
      console.log(`      - Accessories with buffs: ${accessoriesWithBuffs}/${items.length}`)
      console.log(`      - Accessories with rarity: ${accessoriesWithRarity}/${items.length}`)
      break

    case 'npc':
      const npcsWithServices = items.filter(item => item.npcData?.services?.length > 0).length
      const npcsWithQuests = items.filter(item => item.npcData?.questRequirements?.length > 0).length
      console.log(`      - NPCs with services: ${npcsWithServices}/${items.length}`)
      console.log(`      - NPCs with quest data: ${npcsWithQuests}/${items.length}`)
      break

    case 'quest':
      const questsWithSteps = items.filter(item => item.questData?.steps?.length > 0).length
      const questsWithDifficulty = items.filter(item => item.questData?.difficulty).length
      console.log(`      - Quests with steps: ${questsWithSteps}/${items.length}`)
      console.log(`      - Quests with difficulty: ${questsWithDifficulty}/${items.length}`)
      break

    case 'raid':
    case 'enemy':
      const enemiesWithAttacks = items.filter(item => item.enemyData?.attacks?.length > 0).length
      const enemiesWithStats = items.filter(item => item.enemyData?.hp || item.enemyData?.level).length
      console.log(`      - Enemies with attacks: ${enemiesWithAttacks}/${items.length}`)
      console.log(`      - Enemies with stats: ${enemiesWithStats}/${items.length}`)
      break

    case 'mechanic':
      const mechanicsWithPurpose = items.filter(item => item.mechanicData?.purpose).length
      const mechanicsWithMechanics = items.filter(item => item.mechanicData?.mechanics?.length > 0).length
      console.log(`      - Mechanics with purpose: ${mechanicsWithPurpose}/${items.length}`)
      console.log(`      - Mechanics with details: ${mechanicsWithMechanics}/${items.length}`)
      break
  }
}

function calculateCategoryQuality(items: any[]): number {
  if (items.length === 0) return 0

  let totalScore = 0

  items.forEach((item) => {
    let itemScore = 0

    // Champs de base (40 points)
    if (item.name) itemScore += 10
    if (item.type && item.type !== "Unknown") itemScore += 15
    if (item.category) itemScore += 15

    // Champs optionnels généraux (30 points)
    if (item.rarity) itemScore += 5
    if (item.price || item.robuxPrice) itemScore += 5
    if (item.description && item.description.length > 50) itemScore += 8
    if (item.location) itemScore += 4
    if (item.imageUrl) itemScore += 3
    if (item.wikiUrl) itemScore += 5

    // Données spécifiques par type (30 points)
    if (item.fruitData) {
      if (item.fruitData.rarity) itemScore += 5
      if (item.fruitData.priceData?.current?.money || item.fruitData.priceData?.current?.robux) itemScore += 5
      if (item.fruitData.type) itemScore += 8
      if (item.fruitData.awakening !== undefined) itemScore += 5
      if (item.fruitData.pros?.length > 0) itemScore += 5
      if (item.fruitData.cons?.length > 0) itemScore += 5
      if (item.fruitData.trivia?.length > 0) itemScore += 4
      if (item.fruitData.masteryRequirements) itemScore += 3
    }

    if (item.materialData) {
      if (item.materialData.locations?.length > 0) itemScore += 10
      if (item.materialData.usage?.length > 0) itemScore += 10
      if (item.materialData.berryTypes?.length > 0) itemScore += 5
      if (item.materialData.totalRequired) itemScore += 5
    }

    if (item.weaponData) {
      if (item.weaponData.weaponType) itemScore += 8
      if (item.weaponData.damage) itemScore += 5
      if (item.weaponData.stats?.length > 0) itemScore += 8
      if (item.weaponData.pros?.length > 0) itemScore += 5
      if (item.weaponData.upgradeRequirements?.length > 0) itemScore += 4
    }

    if (item.accessoryData) {
      if (item.accessoryData.buffs?.length > 0) itemScore += 10
      if (item.accessoryData.rarity) itemScore += 5
      if (item.accessoryData.pros?.length > 0) itemScore += 5
      if (item.accessoryData.stacksWith?.length > 0) itemScore += 5
      if (item.accessoryData.trivia?.length > 0) itemScore += 5
    }

    if (item.npcData) {
      if (item.npcData.npcType) itemScore += 8
      if (item.npcData.services?.length > 0) itemScore += 8
      if (item.npcData.questRequirements?.length > 0) itemScore += 7
      if (item.npcData.questRewards?.length > 0) itemScore += 7
    }

    if (item.questData) {
      if (item.questData.questGiver) itemScore += 5
      if (item.questData.difficulty) itemScore += 5
      if (item.questData.steps?.length > 0) itemScore += 10
      if (item.questData.requirements?.length > 0) itemScore += 5
      if (item.questData.rewards?.length > 0) itemScore += 5
    }

    if (item.enemyData) {
      if (item.enemyData.enemyType) itemScore += 5
      if (item.enemyData.hp) itemScore += 5
      if (item.enemyData.level) itemScore += 5
      if (item.enemyData.attacks?.length > 0) itemScore += 10
      if (item.enemyData.immunity?.length > 0) itemScore += 5
    }

    if (item.mechanicData) {
      if (item.mechanicData.purpose) itemScore += 8
      if (item.mechanicData.triggeredBy) itemScore += 5
      if (item.mechanicData.mechanics?.length > 0) itemScore += 10
      if (item.mechanicData.notes?.length > 0) itemScore += 4
      if (item.mechanicData.restrictions?.length > 0) itemScore += 3
    }

    totalScore += Math.min(itemScore, 100) // Cap à 100 points par item
  })

  return Math.round(totalScore / items.length)
}

function displayFinalStats(stats: ScrapingStats) {
  console.log("\n" + "=".repeat(60))
  console.log("📊 COMPREHENSIVE SCRAPING RESULTS (Modular Architecture v3.0)")
  console.log("=".repeat(60))

  console.log(`⏱️  Duration: ${Math.round(stats.duration / 1000)}s`)
  console.log(`📦 Total Items: ${stats.totalItems}`)

  console.log("\n📋 By Category:")
  const categoryEmojis: Record<string, string> = {
    fruit: "🍎",
    material: "🧱",
    sword: "⚔️",
    gun: "🔫",
    accessory: "💍",
    npc: "👤",
    quest: "📋",
    raid: "👹",
    enemy: "👹",
    mechanic: "⚙️"
  }

  Object.entries(stats.byCategory).forEach(([category, count]) => {
    const quality = stats.dataQuality[category] || 0
    const emoji = categoryEmojis[category] || "📄"
    console.log(`  ${emoji} ${category.padEnd(12)}: ${count.toString().padStart(3)} items (${quality}% quality)`)
  })

  if (stats.errors.length > 0) {
    console.log("\n❌ Errors:")
    stats.errors.forEach((error) => console.log(`  - ${error}`))
  }

  const avgQuality = Object.values(stats.dataQuality).length > 0
    ? Object.values(stats.dataQuality).reduce((a, b) => a + b, 0) / Object.values(stats.dataQuality).length
    : 0

  console.log(`\n🎯 Average Data Quality: ${Math.round(avgQuality)}%`)
  console.log(`🚀 Architecture: Modular (9 specialized scrapers)`)
  console.log(`📈 Improvement: Enhanced data extraction with category-specific parsing`)
}

async function saveScrapingStats(db: any, stats: ScrapingStats) {
  try {
    const collection = db.collection("scraping_stats")
    await collection.insertOne({
      ...stats,
      timestamp: new Date(),
      version: "3.0", // Modular architecture version
      architecture: "modular",
      scrapers: 9, // Base + 8 specialized scrapers
    })
    console.log("💾 Scraping stats saved to database")
  } catch (error) {
    console.error("Failed to save scraping stats:", error)
  }
}

async function sendDiscordNotification(stats: ScrapingStats) {
  try {
    const webhook = process.env.DISCORD_WEBHOOK_URL!
    const avgQuality =
      Object.values(stats.dataQuality).reduce((a, b) => a + b, 0) / Object.values(stats.dataQuality).length

    const embed = {
      title: "🎮 Blox Fruits Database Updated (v3.0)",
      description: `Comprehensive scraping completed using the new modular architecture with 9 specialized scrapers.`,
      color: stats.errors.length > 0 ? 0xff9900 : 0x00ff00,
      fields: [
        {
          name: "📊 Total Items",
          value: stats.totalItems.toString(),
          inline: true,
        },
        {
          name: "⏱️ Duration",
          value: `${Math.round(stats.duration / 1000)}s`,
          inline: true,
        },
        {
          name: "🎯 Avg Quality",
          value: `${Math.round(avgQuality)}%`,
          inline: true,
        },
        {
          name: "📋 Breakdown",
          value: Object.entries(stats.byCategory)
            .map(([cat, count]) => `${cat}: ${count}`)
            .join("\n"),
          inline: false,
        },
      ],
      footer: {
        text: "Blox Fruits Calculator - Modular Scraper v3.0 (9 Scrapers)",
      },
      timestamp: new Date().toISOString(),
    }

    if (stats.errors.length > 0) {
      embed.fields.push({
        name: "⚠️ Errors",
        value:
          stats.errors.slice(0, 3).join("\n") +
          (stats.errors.length > 3 ? `\n... and ${stats.errors.length - 3} more` : ""),
        inline: false,
      })
    }

    await fetch(webhook, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ embeds: [embed] }),
    })

    console.log("📢 Discord notification sent!")
  } catch (error) {
    console.error("Failed to send Discord notification:", error)
  }
}

// Gestion des signaux pour arrêt propre
process.on("SIGINT", () => {
  console.log("\n⏹️ Comprehensive scraping interrupted by user")
  console.log("🔄 The modular architecture allows for easy resumption of scraping")
  process.exit(0)
})

runComprehensiveScraper().catch(console.error)
