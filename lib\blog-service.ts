import { BlogPost, Comment, BlogService as BlogServiceClass } from "./models/blog"
import { connectToDatabase } from "./mongodb"

// Export du service pour utilisation dans l'application
export const BlogServiceFinal = BlogServiceClass

// Types utilitaires
export type { BlogPostType as BlogPost, CommentType as Comment } from "./models/blog"

// Fonctions utilitaires pour le blog
export function calculateReadTime(content: string): number {
  const wordsPerMinute = 200
  const words = content.trim().split(/\s+/).length
  return Math.ceil(words / wordsPerMinute)
}

export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/(^-|-$)/g, "")
}

export function formatDate(date: string | Date): string {
  return new Date(date).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  })
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength).trim() + "..."
}

export class BlogService {
  static async getAllPosts(
    options: {
      page?: number
      limit?: number
      category?: string
      search?: string
      featured?: boolean
      status?: "draft" | "published"
    } = {},
  ) {
    await connectToDatabase()

    const { page = 1, limit = 10, category, search, featured, status = "published" } = options

    const query: any = { status }

    if (category && category !== "All") {
      query.category = category
    }

    if (featured !== undefined) {
      query.featured = featured
    }

    if (search) {
      query.$text = { $search: search }
    }

    const posts = await BlogPost.find(query)
      .sort({ publishedAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean()

    const total = await BlogPost.countDocuments(query)

    return {
      posts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    }
  }

  static async getPostBySlug(slug: string) {
    await connectToDatabase()

    const post = await BlogPost.findOne({ slug, status: "published" }).lean()

    if (post && typeof post === "object" && !Array.isArray(post)) {
      // Increment view count
      await BlogPost.updateOne({ _id: (post as any)._id }, { $inc: { views: 1 } })
    }

    return post
  }

  static async createPost(postData: any) {
    await connectToDatabase()

    // Generate slug from title if not provided
    if (!postData.slug) {
      postData.slug = generateSlug(postData.title)
    }

    // Calculate read time (average 200 words per minute)
    postData.readTime = calculateReadTime(postData.content)

    const post = new BlogPost(postData)
    return await post.save()
  }

  static async updatePost(id: string, updateData: any) {
    await connectToDatabase()

    updateData.updatedAt = new Date()

    // Recalculate read time if content changed
    if (updateData.content) {
      updateData.readTime = calculateReadTime(updateData.content)
    }

    return await BlogPost.findByIdAndUpdate(id, updateData, { new: true })
  }

  static async deletePost(id: string) {
    await connectToDatabase()

    // Delete associated comments
    await Comment.deleteMany({ postId: id })

    return await BlogPost.findByIdAndDelete(id)
  }

  static async getCategories() {
    await connectToDatabase()

    const categories = await BlogPost.distinct("category", { status: "published" })
    return categories.sort()
  }

  static async getTags() {
    await connectToDatabase()

    const tags = await BlogPost.distinct("tags", { status: "published" })
    return tags.sort()
  }

  static async getRelatedPosts(postId: string, category: string, limit = 3) {
    await connectToDatabase()

    return await BlogPost.find({
      _id: { $ne: postId },
      category,
      status: "published",
    })
      .sort({ publishedAt: -1 })
      .limit(limit)
      .lean()
  }

  static async getPopularPosts(limit = 5) {
    await connectToDatabase()

    return await BlogPost.find({ status: "published" }).sort({ views: -1, likes: -1 }).limit(limit).lean()
  }

  static async searchPosts(query: string, limit = 10) {
    await connectToDatabase()

    return await BlogPost.find({
      $text: { $search: query },
      status: "published",
    })
      .sort({ score: { $meta: "textScore" } })
      .limit(limit)
      .lean()
  }

  // Comment methods
  static async getComments(postId: string) {
    await connectToDatabase()

    const comments = await Comment.find({ postId, status: "approved", parentId: null }).sort({ publishedAt: -1 }).lean()

    // Get replies for each comment
    for (const comment of comments) {
      const replies = await Comment.find({ parentId: (comment as any)._id, status: "approved" })
        .sort({ publishedAt: 1 })
        .lean()
      ;(comment as any).replies = replies
    }

    return comments
  }

  static async createComment(commentData: any) {
    await connectToDatabase()

    const comment = new Comment(commentData)
    const savedComment = await comment.save()

    // Increment comment count on post
    await BlogPost.updateOne({ _id: commentData.postId }, { $inc: { comments: 1 } })

    return savedComment
  }

  static async likePost(postId: string) {
    await connectToDatabase()

    return await BlogPost.updateOne({ _id: postId }, { $inc: { likes: 1 } })
  }

  static async likeComment(commentId: string) {
    await connectToDatabase()

    return await Comment.updateOne({ _id: commentId }, { $inc: { likes: 1 } })
  }
}
