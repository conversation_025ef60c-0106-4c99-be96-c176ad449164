import Link from "next/link"
import { Calculator, Database, TrendingUp, Zap, Users, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AdBanner } from "@/components/ads/ad-banner"

export default function HomePage() {
  const calculators = [
    {
      title: "Stats Calculator",
      description: "Optimize your stat distribution for maximum effectiveness",
      icon: Calculator,
      href: "/calculator/stats",
      color: "from-blue-500 to-cyan-500",
    },
    {
      title: "Damage Calculator",
      description: "Calculate precise damage output for any build",
      icon: Zap,
      href: "/calculator/damage",
      color: "from-red-500 to-orange-500",
    },
    {
      title: "Farming Calculator",
      description: "Find the most efficient farming spots for your level",
      icon: Clock,
      href: "/calculator/farming",
      color: "from-green-500 to-emerald-500",
    },
    {
      title: "Trading Calculator",
      description: "Get accurate trading values and market trends",
      icon: TrendingUp,
      href: "/calculator/trading",
      color: "from-purple-500 to-pink-500",
    },
  ]

  const stats = [
    { label: "Active Users", value: "50K+", icon: Users },
    { label: "Calculations", value: "2M+", icon: Calculator },
    { label: "Items Tracked", value: "500+", icon: Database },
  ]

  return (
    <div className="space-y-16">
      {/* Top Banner Ad */}
      <div className="container mx-auto px-4 pt-4">
        <AdBanner adSlot="1234567890" adFormat="horizontal" className="max-w-4xl mx-auto" />
      </div>

      {/* Hero Section */}
      <section className="relative py-20 px-4">
        <div className="container mx-auto text-center">
          <h1 className="font-gaming text-4xl md:text-6xl font-bold mb-6 neon-text">Master Blox Fruits</h1>
          <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            The ultimate calculator suite for Blox Fruits. Optimize your stats, calculate damage, find the best farming
            spots, and track trading values.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="glow-effect" asChild>
              <Link href="/calculator/stats">Start Calculating</Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/database">Browse Database</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {stats.map((stat, index) => (
              <Card key={index} className="gaming-card text-center">
                <CardContent className="pt-6">
                  <stat.icon className="w-12 h-12 mx-auto mb-4 text-primary" />
                  <div className="font-mono text-3xl font-bold mb-2">{stat.value}</div>
                  <div className="text-muted-foreground">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Calculators Grid */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="font-gaming text-3xl font-bold mb-4">Powerful Calculators</h2>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Everything you need to optimize your Blox Fruits gameplay
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {calculators.map((calc, index) => (
              <Card key={index} className="gaming-card group hover:scale-105 transition-transform duration-300">
                <CardHeader>
                  <div
                    className={`w-12 h-12 rounded-lg bg-gradient-to-r ${calc.color} flex items-center justify-center mb-4`}
                  >
                    <calc.icon className="w-6 h-6 text-white" />
                  </div>
                  <CardTitle className="font-gaming">{calc.title}</CardTitle>
                  <CardDescription>{calc.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full glow-effect" asChild>
                    <Link href={calc.href}>Use Calculator</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12 px-4 bg-card/20">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="font-gaming text-3xl font-bold mb-4">Why Choose Our Calculator?</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center mx-auto mb-4">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <h3 className="font-gaming text-xl font-semibold mb-2">Real-time Updates</h3>
              <p className="text-muted-foreground">
                Our data is updated hourly to ensure you always have the latest game information.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center mx-auto mb-4">
                <Calculator className="w-8 h-8 text-white" />
              </div>
              <h3 className="font-gaming text-xl font-semibold mb-2">Precise Calculations</h3>
              <p className="text-muted-foreground">
                Based on official game formulas and extensively tested by the community.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="font-gaming text-xl font-semibold mb-2">Community Driven</h3>
              <p className="text-muted-foreground">
                Built by players, for players. Join thousands of Blox Fruits enthusiasts.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
