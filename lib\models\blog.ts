import { Schema, model, models } from "mongoose"

const AuthorSchema = new Schema({
  name: { type: String, required: true },
  avatar: { type: String, required: true },
  bio: { type: String, required: true },
  email: { type: String, required: true },
  social: {
    twitter: String,
    youtube: String,
    discord: String,
  },
})

const SEOSchema = new Schema({
  metaTitle: { type: String, required: true },
  metaDescription: { type: String, required: true },
  keywords: [{ type: String }],
  canonicalUrl: String,
  ogImage: String,
})

const BlogPostSchema = new Schema(
  {
    title: { type: String, required: true },
    slug: { type: String, required: true, unique: true },
    excerpt: { type: String, required: true },
    content: { type: String, required: true },
    author: { type: AuthorSchema, required: true },
    category: { type: String, required: true },
    tags: [{ type: String }],
    publishedAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    comments: { type: Number, default: 0 },
    featured: { type: Boolean, default: false },
    status: { type: String, enum: ["draft", "published"], default: "draft" },
    readTime: { type: Number, required: true },
    seo: { type: SEOSchema, required: true },
    featuredImage: String,
    gallery: [String],
  },
  {
    timestamps: true,
  },
)

// Index for search functionality
BlogPostSchema.index({ title: "text", excerpt: "text", content: "text", tags: "text" })
BlogPostSchema.index({ slug: 1 })
BlogPostSchema.index({ category: 1 })
BlogPostSchema.index({ publishedAt: -1 })
BlogPostSchema.index({ featured: 1 })
BlogPostSchema.index({ status: 1 })

export const BlogPost = models.BlogPost || model("BlogPost", BlogPostSchema)

const CommentSchema = new Schema(
  {
    postId: { type: Schema.Types.ObjectId, ref: "BlogPost", required: true },
    author: {
      name: { type: String, required: true },
      email: { type: String, required: true },
      avatar: String,
      userId: { type: Schema.Types.ObjectId, ref: "User" },
    },
    content: { type: String, required: true },
    parentId: { type: Schema.Types.ObjectId, ref: "Comment" },
    likes: { type: Number, default: 0 },
    status: { type: String, enum: ["pending", "approved", "rejected"], default: "pending" },
    publishedAt: { type: Date, default: Date.now },
  },
  {
    timestamps: true,
  },
)

CommentSchema.index({ postId: 1 })
CommentSchema.index({ parentId: 1 })
CommentSchema.index({ publishedAt: -1 })

export const Comment = models.Comment || model("Comment", CommentSchema)

// Types d'interface
export interface Author {
  name: string
  avatar: string
  bio: string
  email: string
  social?: {
    twitter?: string
    youtube?: string
    discord?: string
  }
}

export interface SEO {
  metaTitle: string
  metaDescription: string
  keywords: string[]
  canonicalUrl?: string
  ogImage?: string
}

export interface BlogPostType {
  _id: string
  title: string
  slug: string
  excerpt: string
  content: string
  author: Author
  category: string
  tags: string[]
  publishedAt: Date
  updatedAt: Date
  views: number
  likes: number
  comments: number
  featured: boolean
  status: "draft" | "published"
  readTime: number
  seo: SEO
  featuredImage?: string
  gallery?: string[]
}

export interface CommentType {
  _id: string
  postId: string
  author: {
    name: string
    email: string
    avatar?: string
    userId?: string
  }
  content: string
  parentId?: string
  likes: number
  status: "pending" | "approved" | "rejected"
  publishedAt: Date
  createdAt?: Date
  updatedAt?: Date
}

// Service de blog
export class BlogService {
  static async getPosts(options?: {
    status?: "draft" | "published" | "all"
    category?: string
    limit?: number
    skip?: number
    search?: string
  }) {
    try {
      const { status = "published", category, limit = 10, skip = 0, search } = options || {}

      const query: any = {}
      if (status !== "all") {
        query.status = status
      }
      if (category && category !== "All") {
        query.category = category
      }
      if (search) {
        query.$text = { $search: search }
      }

      const posts = await BlogPost.find(query).sort({ publishedAt: -1 }).skip(skip).limit(limit).lean()

      const total = await BlogPost.countDocuments(query)

      return {
        posts: posts as unknown as BlogPostType[],
        total,
        hasMore: skip + limit < total,
      }
    } catch (error) {
      console.error("Error fetching posts:", error)
      return {
        posts: [] as BlogPostType[],
        total: 0,
        hasMore: false,
      }
    }
  }

  static async getPostBySlug(slug: string): Promise<BlogPostType | null> {
    try {
      const post = await BlogPost.findOne({ slug, status: "published" }).lean()

      if (post && typeof post === "object" && !Array.isArray(post)) {
        // Increment view count
        await BlogPost.updateOne({ _id: (post as any)._id }, { $inc: { views: 1 } })
        return post as unknown as BlogPostType
      }

      return null
    } catch (error) {
      console.error("Error fetching post by slug:", error)
      return null
    }
  }

  static async createPost(postData: Partial<BlogPostType>) {
    try {
      // Generate slug from title if not provided
      if (!postData.slug && postData.title) {
        postData.slug = postData.title
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, "-")
          .replace(/(^-|-$)/g, "")
      }

      // Calculate read time (average 200 words per minute)
      if (postData.content) {
        const wordsPerMinute = 200
        const words = postData.content.trim().split(/\s+/).length
        postData.readTime = Math.ceil(words / wordsPerMinute)
      }

      const post = new BlogPost(postData)
      const savedPost = await post.save()
      return savedPost as unknown as BlogPostType
    } catch (error) {
      console.error("Error creating post:", error)
      throw error
    }
  }

  static async updatePost(id: string, postData: Partial<BlogPostType>) {
    try {
      postData.updatedAt = new Date()

      // Recalculate read time if content changed
      if (postData.content) {
        const wordsPerMinute = 200
        const words = postData.content.trim().split(/\s+/).length
        postData.readTime = Math.ceil(words / wordsPerMinute)
      }

      const updatedPost = await BlogPost.findByIdAndUpdate(id, postData, { new: true })
      return updatedPost as unknown as BlogPostType
    } catch (error) {
      console.error("Error updating post:", error)
      throw error
    }
  }

  static async deletePost(id: string) {
    try {
      // Delete associated comments
      await Comment.deleteMany({ postId: id })
      return await BlogPost.findByIdAndDelete(id)
    } catch (error) {
      console.error("Error deleting post:", error)
      throw error
    }
  }

  static async getComments(postId: string) {
    try {
      const comments = await Comment.find({ postId, status: "approved" }).sort({ publishedAt: -1 }).lean()
      return comments as unknown as CommentType[]
    } catch (error) {
      console.error("Error fetching comments:", error)
      return []
    }
  }

  static async createComment(commentData: Partial<CommentType>) {
    try {
      const comment = new Comment(commentData)
      const savedComment = await comment.save()

      // Increment comment count on post
      if (commentData.postId) {
        await BlogPost.updateOne({ _id: commentData.postId }, { $inc: { comments: 1 } })
      }

      return savedComment as unknown as CommentType
    } catch (error) {
      console.error("Error creating comment:", error)
      throw error
    }
  }
}
