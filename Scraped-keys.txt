_id
_id.$oid
name
category
description
fruitData
fruitData.type
fruitData.rarity
fruitData.introducedUpdate
fruitData.m1Capability
fruitData.awakening
fruitData.transformation
fruitData.mainDescription
fruitData.combatRating
fruitData.combatRating.pvp
fruitData.combatRating.grinding
fruitData.furyMeterMechanics
fruitData.furyMeterMechanics.description
fruitData.furyMeterMechanics.orangeMeter
fruitData.furyMeterMechanics.orangeMeter.fillMethod
fruitData.furyMeterMechanics.orangeMeter.requiredFor
fruitData.furyMeterMechanics.orangeMeter.drainTriggers
fruitData.furyMeterMechanics.orangeMeter.description
fruitData.furyMeterMechanics.redMeter
fruitData.furyMeterMechanics.redMeter.fillMethod
fruitData.furyMeterMechanics.redMeter.requiredFor
fruitData.furyMeterMechanics.redMeter.drainTriggers
fruitData.furyMeterMechanics.redMeter.description
fruitData.furyMeterMechanics.transformationMechanics
fruitData.furyMeterMechanics.transformationMechanics.passiveDrain
fruitData.furyMeterMechanics.transformationMechanics.allowsIndefiniteTransformation
fruitData.furyMeterMechanics.transformationMechanics.hybridRequirement
fruitData.furyMeterMechanics.transformationMechanics.fullTransformationRequirement
fruitData.furyMeterMechanics.transformationMechanics.drainBehavior
fruitData.damageResistance
fruitData.damageResistance.human
fruitData.damageResistance.hybrid
fruitData.damageResistance.hybrid.players
fruitData.damageResistance.hybrid.npcs
fruitData.damageResistance.transformed
fruitData.skinSystem
fruitData.skinSystem.defaultSkin
fruitData.skinSystem.craftableSkins
fruitData.skinSystem.chromaticSkins
fruitData.skinSystem.acquisition
fruitData.skinSystem.acquisition.crafting
fruitData.skinSystem.acquisition.purchase
fruitData.skinSystem.acquisition.chromatic
fruitData.variantsComplete
fruitData.variantsComplete.eastern
fruitData.variantsComplete.eastern.name
fruitData.variantsComplete.eastern.theme
fruitData.variantsComplete.eastern.mountingCapacity
fruitData.variantsComplete.eastern.specialMechanics
fruitData.variantsComplete.eastern.movementStyle
fruitData.variantsComplete.eastern.culturalReference
fruitData.variantsComplete.eastern.description
fruitData.variantsComplete.western
fruitData.variantsComplete.western.name
fruitData.variantsComplete.western.theme
fruitData.variantsComplete.western.mountingCapacity
fruitData.variantsComplete.western.specialMechanics
fruitData.variantsComplete.western.movementStyle
fruitData.variantsComplete.western.culturalReference
fruitData.variantsComplete.western.description
fruitData.variantsComplete.western.flightSpeed
fruitData.variants
fruitData.variants.[].name
fruitData.variants.[].type
fruitData.variants.[].mountingCapacity
fruitData.variants.[].culturalTheme
fruitData.variants.[].characteristics
fruitData.reworkDetails
fruitData.economicData
fruitData.economicData.acquisitionMethods
fruitData.economicData.acquisitionMethods.[].source
fruitData.economicData.reworkHistory
fruitData.economicData.reworkHistory.majorUpdate
fruitData.economicData.reworkHistory.tokenSystem
fruitData.economicData.reworkHistory.priceIncrease
fruitData.economicData.reworkHistory.priceIncrease.robux
fruitData.economicData.competitiveRanking
fruitData.economicData.competitiveRanking.difficulty
fruitData.economicData.competitiveRanking.grinding
fruitData.economicData.competitiveRanking.pvp
fruitData.economicData.marketAnalysis
fruitData.economicData.marketAnalysis.pricePosition
fruitData.economicData.marketAnalysis.availability
fruitData.economicData.marketAnalysis.tradeValue
fruitData.changeHistoryAdvanced
fruitData.changeHistoryAdvanced.[].update
fruitData.changeHistoryAdvanced.[].changes
fruitData.changeHistoryAdvanced.[].changes.[].description
fruitData.changeHistoryAdvanced.[].changes.[].type
fruitData.changeHistoryAdvanced.[].type
fruitData.forms
fruitData.forms.[].name
fruitData.forms.[].type
fruitData.forms.[].moves
fruitData.forms.[].moves.[].key
fruitData.forms.[].moves.[].name
fruitData.forms.[].moves.[].description
fruitData.forms.[].moves.[].mastery
fruitData.forms.[].moves.[].gif
fruitData.forms.[].moves.[].gif1
fruitData.forms.[].moves.[].gif2
fruitData.forms.[].moves.[].damage
fruitData.forms.[].moves.[].cooldown
fruitData.forms.[].moves.[].energy
fruitData.forms.[].moves.[].energy.type
fruitData.forms.[].moves.[].energy.value
fruitData.forms.[].moves.[].energy.display
fruitData.forms.[].moves.[].furyMeter
fruitData.passiveAbilities
fruitData.passiveAbilities.[].name
fruitData.passiveAbilities.[].description
fruitData.passiveAbilities.[].showcaseUrl
fruitData.masteryRequirements
fruitData.masteryRequirements.Heatwave Cannon
fruitData.masteryRequirements.Infernal Pincer
fruitData.masteryRequirements.Scorching Downfall
fruitData.masteryRequirements.Imperial Evolution
fruitData.masteryRequirements.Draconic Soar
fruitData.priceData
fruitData.priceData.current
fruitData.priceData.current.money
fruitData.priceData.current.status
fruitData.priceData.current.robux
fruitData.priceData.sources
fruitData.priceData.historical
fruitData.priceData.historical.[].money
fruitData.priceData.historical.[].context
fruitData.priceData.historical.[].type
fruitData.trivia
fruitData.gallery
fruitData.gallery.[].url
fruitData.gallery.[].caption
fruitData.shopQuote
fruitData.pros
fruitData.cons
fruitData.formSpecific
fruitData.formSpecific.Transformed (East)
fruitData.formSpecific.Transformed (East).pros
fruitData.formSpecific.Transformed (East).cons
fruitData.formSpecific.Transformed (West)
fruitData.formSpecific.Transformed (West).pros
fruitData.formSpecific.Transformed (West).cons
imageUrl
imageUrls
lastUpdated
lastUpdated.$date
rawData
rawData.infobox
rawData.infobox.name
rawData.infobox.tab1
rawData.infobox.image1
rawData.infobox.tab2
rawData.infobox.image2
rawData.infobox.tab3
rawData.infobox.image3
rawData.infobox.type
rawData.infobox.rarity
rawData.infobox.m1
rawData.infobox.update
rawData.infobox.money
rawData.wikitextLength
rawData.movesFound
rawData.statsFound
rawData.extractedAt
type
wikiUrl