"use client"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import {
  ArrowLeft,
  Star,
  Zap,
  Shield,
  TrendingUp,
  Clock,
  Target,
  Gamepad2,
  Info,
  ThumbsUp,
  ThumbsDown,
} from "lucide-react"

interface DatabaseItem {
  id: string
  name: string
  type: "fruit" | "sword" | "accessory" | "gun" | "material"
  rarity: "Common" | "Uncommon" | "Rare" | "Legendary" | "Mythical"
  imageUrl: string
  price?: string
  robuxPrice?: string
  obtainment?: string
  stats?: string[]
  description?: string
  damage?: number
  mastery?: number
  special?: string[]
  awakened?: boolean
  value?: string
  detailedStats?: {
    attack: number
    defense: number
    speed: number
    range: number
    difficulty: number
  }
  moves?: Array<{
    name: string
    key: string
    damage: number
    cooldown: number
    mastery: number
    description: string
  }>
  tips?: string[]
  combos?: string[]
  pros?: string[]
  cons?: string[]
}

export default function DatabaseItemPage() {
  const params = useParams()
  const type = params.type as string
  const id = params.id as string

  // Base de données étendue avec détails complets
  const allItems: DatabaseItem[] = [
    {
      id: "leopard-fruit",
      name: "Leopard",
      type: "fruit",
      rarity: "Mythical",
      imageUrl: "/placeholder.svg?height=200&width=200",
      price: "5,000,000",
      robuxPrice: "3,000",
      description:
        "The most expensive and powerful fruit in the game. A Zoan-type Devil Fruit that allows the user to transform into a leopard hybrid and full leopard form.",
      stats: ["Highest Damage", "Speed Boost", "Stealth Mode", "Transformation"],
      damage: 95,
      mastery: 600,
      special: [
        "Afterimage Assault",
        '\
      damage: 95,\
      mastery: 600,\
      special: ["Afterimage Assault',
        "Body Flicker",
        "Spiraling Kick",
      ],
      awakened: false,
      value: "5M",
      detailedStats: {
        attack: 95,
        defense: 70,
        speed: 90,
        range: 80,
        difficulty: 85,
      },
      moves: [
        {
          name: "Afterimage Assault",
          key: "Z",
          damage: 85,
          cooldown: 8,
          mastery: 1,
          description: "Dash forward with incredible speed, leaving afterimages behind",
        },
        {
          name: "Body Flicker",
          key: "X",
          damage: 90,
          cooldown: 12,
          mastery: 100,
          description: "Teleport behind enemies and strike with devastating force",
        },
        {
          name: "Spiraling Kick",
          key: "C",
          damage: 95,
          cooldown: 15,
          mastery: 200,
          description: "Perform a spinning kick that hits multiple enemies",
        },
        {
          name: "Transformation",
          key: "V",
          damage: 0,
          cooldown: 20,
          mastery: 350,
          description: "Transform into full leopard form for enhanced abilities",
        },
      ],
      tips: [
        "Use Afterimage Assault to close gaps quickly",
        "Body Flicker is great for escaping dangerous situations",
        "Transformation mode increases all damage by 40%",
        "Combine moves for devastating combos",
      ],
      combos: [
        "Z + X + C (Basic combo for high damage)",
        "V + Z + X + C (Full transformation combo)",
        "X + C + Z (Teleport combo for surprise attacks)",
      ],
      pros: [
        "Highest damage output in the game",
        "Excellent mobility and speed",
        "Great for both PvP and PvE",
        "Transformation provides significant buffs",
      ],
      cons: [
        "Very expensive to obtain",
        "High mastery requirements",
        "Vulnerable during transformation animation",
        "Requires skill to master effectively",
      ],
    },
    {
      id: "dragon-fruit",
      name: "Dragon",
      type: "fruit",
      rarity: "Mythical",
      imageUrl: "/placeholder.svg?height=200&width=200",
      price: "3,500,000",
      robuxPrice: "2,600",
      description: "One of the most powerful Zoan-type Devil Fruits that allows the user to transform into a dragon.",
      stats: ["Extremely High Damage", "Flight Ability", "Transformation", "AOE Attacks"],
      damage: 90,
      mastery: 500,
      special: ["Dragon Rush", "Fire Shower", "Heatwave Beam"],
      awakened: true,
      value: "3.5M",
      detailedStats: {
        attack: 90,
        defense: 85,
        speed: 75,
        range: 95,
        difficulty: 70,
      },
      moves: [
        {
          name: "Dragon Rush",
          key: "Z",
          damage: 80,
          cooldown: 10,
          mastery: 1,
          description: "Rush forward in dragon form dealing massive damage",
        },
        {
          name: "Fire Shower",
          key: "X",
          damage: 85,
          cooldown: 12,
          mastery: 100,
          description: "Rain down fire from above hitting multiple enemies",
        },
        {
          name: "Heatwave Beam",
          key: "C",
          damage: 95,
          cooldown: 18,
          mastery: 250,
          description: "Shoot a powerful beam of heat energy",
        },
        {
          name: "Transformation",
          key: "V",
          damage: 0,
          cooldown: 25,
          mastery: 400,
          description: "Transform into full dragon form with flight",
        },
      ],
      tips: [
        "Use flight to gain tactical advantage",
        "Fire Shower is excellent for crowd control",
        "Transformation allows unlimited flight",
        "Great for raids and boss fights",
      ],
      combos: ["Z + X + C (Ground combo)", "V + X + C (Aerial combo)", "C + Z + X (Long range to close range)"],
      pros: [
        "Can fly indefinitely in transformation",
        "Excellent AOE damage",
        "Great for both PvP and grinding",
        "Awakened version available",
      ],
      cons: [
        "Slower movement speed",
        "Large hitbox when transformed",
        "High energy consumption",
        "Predictable attack patterns",
      ],
    },
    {
      id: "cursed-dual-katana",
      name: "Cursed Dual Katana",
      type: "sword",
      rarity: "Mythical",
      imageUrl: "/placeholder.svg?height=200&width=200",
      obtainment: "Complete Tushita and Yama puzzle",
      description:
        "The strongest sword in the game, forged from the combination of Tushita and Yama. Possesses incredible cursed power.",
      stats: ["Highest Sword Damage", "Special Abilities", "Dual Wield", "Cursed Power"],
      damage: 95,
      mastery: 600,
      special: ["Cursed Dual Slash", "Yama", "Tushita"],
      value: "Unobtainable",
      detailedStats: {
        attack: 95,
        defense: 60,
        speed: 85,
        range: 75,
        difficulty: 90,
      },
      moves: [
        {
          name: "Cursed Dual Slash",
          key: "Z",
          damage: 90,
          cooldown: 8,
          mastery: 1,
          description: "Perform a devastating dual slash with both blades",
        },
        {
          name: "Yama",
          key: "X",
          damage: 95,
          cooldown: 15,
          mastery: 200,
          description: "Unleash the power of Yama with a dark energy slash",
        },
        {
          name: "Tushita",
          key: "C",
          damage: 100,
          cooldown: 20,
          mastery: 400,
          description: "Channel Tushita's divine power for maximum damage",
        },
      ],
      tips: [
        "Requires both Tushita and Yama to obtain",
        "Best sword for end-game content",
        "Combine with sword stats for maximum effectiveness",
        "Practice timing for optimal damage",
      ],
      combos: ["Z + X + C (Full power combo)", "X + C (High damage finisher)", "Z + C (Quick burst combo)"],
      pros: [
        "Highest sword damage in game",
        "Unique dual-wield mechanics",
        "Incredible special abilities",
        "Status symbol among players",
      ],
      cons: [
        "Extremely difficult to obtain",
        "Requires high mastery to use effectively",
        "No awakening available",
        "Limited range compared to fruits",
      ],
    },
  ]

  const item = allItems.find((i) => i.id === id && i.type === type)

  if (!item) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="gaming-card">
          <CardContent className="text-center py-12">
            <h3 className="text-lg font-semibold mb-2">Item not found</h3>
            <p className="text-muted-foreground mb-4">The requested item could not be found.</p>
            <Button asChild>
              <Link href="/database">Back to Database</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case "Common":
        return "bg-gray-500/20 text-gray-400 border-gray-500/50"
      case "Uncommon":
        return "bg-green-500/20 text-green-400 border-green-500/50"
      case "Rare":
        return "bg-blue-500/20 text-blue-400 border-blue-500/50"
      case "Legendary":
        return "bg-purple-500/20 text-purple-400 border-purple-500/50"
      case "Mythical":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/50"
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/50"
    }
  }

  const getTypeInfo = (type: string) => {
    switch (type) {
      case "fruit":
        return { name: "Devil Fruit", icon: "🍎", color: "text-red-400" }
      case "sword":
        return { name: "Sword", icon: "⚔️", color: "text-blue-400" }
      case "accessory":
        return { name: "Accessory", icon: "👑", color: "text-purple-400" }
      case "gun":
        return { name: "Gun", icon: "🔫", color: "text-orange-400" }
      case "material":
        return { name: "Material", icon: "💎", color: "text-green-400" }
      default:
        return { name: "Item", icon: "📦", color: "text-gray-400" }
    }
  }

  const typeInfo = getTypeInfo(item.type)

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Navigation */}
      <div className="flex items-center gap-4 mb-8">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/database/${type}`}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to {typeInfo.name}s
          </Link>
        </Button>
        <span className="text-muted-foreground">/</span>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/database">Database</Link>
        </Button>
      </div>

      {/* Header */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <div className="lg:col-span-1">
          <Card className="gaming-card">
            <CardContent className="p-6">
              <div className="aspect-square bg-secondary/20 rounded-lg flex items-center justify-center overflow-hidden mb-4">
                <img
                  src={item.imageUrl || "/placeholder.svg"}
                  alt={item.name}
                  className="w-full h-full object-cover rounded-lg"
                  onError={(e) => {
                    e.currentTarget.src = "/placeholder.svg?height=200&width=200"
                  }}
                />
              </div>
              <div className="text-center">
                <h1 className="font-gaming text-2xl font-bold mb-2 flex items-center justify-center gap-2">
                  <span className="text-2xl">{typeInfo.icon}</span>
                  {item.name}
                  {item.awakened && <Zap className="w-5 h-5 text-yellow-400" />}
                </h1>
                <div className="flex items-center justify-center gap-2 mb-4">
                  <Badge className={`${getRarityColor(item.rarity)}`}>{item.rarity}</Badge>
                  <Badge variant="outline" className={typeInfo.color}>
                    {typeInfo.name}
                  </Badge>
                </div>
                <p className="text-muted-foreground text-sm">{item.description}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {/* Basic Stats */}
            <Card className="gaming-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5 text-red-400" />
                  Combat Stats
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {item.damage && (
                  <div className="flex justify-between items-center">
                    <span>Damage</span>
                    <span className="font-mono text-red-400">{item.damage}</span>
                  </div>
                )}
                {item.mastery && (
                  <div className="flex justify-between items-center">
                    <span>Max Mastery</span>
                    <span className="font-mono text-blue-400">{item.mastery}</span>
                  </div>
                )}
                {item.awakened !== undefined && (
                  <div className="flex justify-between items-center">
                    <span>Awakened</span>
                    <span className={item.awakened ? "text-yellow-400" : "text-gray-400"}>
                      {item.awakened ? "Yes" : "No"}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Pricing */}
            <Card className="gaming-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="w-5 h-5 text-green-400" />
                  Pricing & Value
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {item.price && (
                  <div className="flex justify-between items-center">
                    <span>Beli Price</span>
                    <span className="font-mono text-primary">{item.price}</span>
                  </div>
                )}
                {item.robuxPrice && (
                  <div className="flex justify-between items-center">
                    <span>Robux Price</span>
                    <span className="font-mono text-yellow-400">{item.robuxPrice}</span>
                  </div>
                )}
                {item.value && (
                  <div className="flex justify-between items-center">
                    <span>Trading Value</span>
                    <span className="font-mono text-green-400">{item.value}</span>
                  </div>
                )}
                {item.obtainment && (
                  <div>
                    <span className="text-sm font-semibold">How to obtain:</span>
                    <p className="text-sm text-muted-foreground mt-1">{item.obtainment}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Detailed Stats */}
          {item.detailedStats && (
            <Card className="gaming-card mb-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5 text-primary" />
                  Detailed Statistics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>Attack</span>
                      <span className="font-mono">{item.detailedStats.attack}/100</span>
                    </div>
                    <Progress value={item.detailedStats.attack} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>Defense</span>
                      <span className="font-mono">{item.detailedStats.defense}/100</span>
                    </div>
                    <Progress value={item.detailedStats.defense} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>Speed</span>
                      <span className="font-mono">{item.detailedStats.speed}/100</span>
                    </div>
                    <Progress value={item.detailedStats.speed} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>Range</span>
                      <span className="font-mono">{item.detailedStats.range}/100</span>
                    </div>
                    <Progress value={item.detailedStats.range} className="h-2" />
                  </div>
                  <div className="md:col-span-2">
                    <div className="flex justify-between mb-2">
                      <span>Difficulty</span>
                      <span className="font-mono">{item.detailedStats.difficulty}/100</span>
                    </div>
                    <Progress value={item.detailedStats.difficulty} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Detailed Information Tabs */}
      <Tabs defaultValue="moves" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="moves" className="flex items-center gap-2">
            <Gamepad2 className="w-4 h-4" />
            Moves
          </TabsTrigger>
          <TabsTrigger value="tips" className="flex items-center gap-2">
            <Info className="w-4 h-4" />
            Tips & Combos
          </TabsTrigger>
          <TabsTrigger value="pros-cons" className="flex items-center gap-2">
            <ThumbsUp className="w-4 h-4" />
            Pros & Cons
          </TabsTrigger>
          <TabsTrigger value="stats" className="flex items-center gap-2">
            <Star className="w-4 h-4" />
            Stats
          </TabsTrigger>
        </TabsList>

        <TabsContent value="moves" className="mt-6">
          {item.moves ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {item.moves.map((move, index) => (
                <Card key={index} className="gaming-card">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        {move.name}
                        <Badge variant="outline" className="text-xs">
                          {move.key}
                        </Badge>
                      </span>
                      <span className="text-red-400 font-mono text-sm">{move.damage} DMG</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-3">{move.description}</p>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-blue-400" />
                        <span>{move.cooldown}s cooldown</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <TrendingUp className="w-4 h-4 text-green-400" />
                        <span>{move.mastery} mastery</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card className="gaming-card">
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">No move information available for this item.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="tips" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {item.tips && (
              <Card className="gaming-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Info className="w-5 h-5 text-blue-400" />
                    Tips & Strategies
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {item.tips.map((tip, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm">
                        <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                        {tip}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {item.combos && (
              <Card className="gaming-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Gamepad2 className="w-5 h-5 text-purple-400" />
                    Combos
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {item.combos.map((combo, index) => (
                      <li key={index} className="bg-secondary/20 p-3 rounded-lg">
                        <code className="text-sm font-mono text-purple-400">{combo}</code>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="pros-cons" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {item.pros && (
              <Card className="gaming-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ThumbsUp className="w-5 h-5 text-green-400" />
                    Advantages
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {item.pros.map((pro, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm">
                        <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                        {pro}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {item.cons && (
              <Card className="gaming-card">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ThumbsDown className="w-5 h-5 text-red-400" />
                    Disadvantages
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {item.cons.map((con, index) => (
                      <li key={index} className="flex items-start gap-2 text-sm">
                        <div className="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                        {con}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="stats" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {item.stats &&
              item.stats.map((stat, index) => (
                <Card key={index} className="gaming-card">
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl mb-2">⭐</div>
                    <div className="font-semibold text-sm">{stat}</div>
                  </CardContent>
                </Card>
              ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
