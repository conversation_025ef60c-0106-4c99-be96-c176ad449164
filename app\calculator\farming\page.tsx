"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Clock, MapPin, TrendingUp, Zap, Target, Award, AlertTriangle } from "lucide-react"

interface NPCData {
  name: string
  level: number
  health: number
  location: string
  sea: 1 | 2 | 3
  rewards: {
    xp: number
    beli: number
  }
  respawnTime: number
  difficulty: "Easy" | "Medium" | "Hard" | "Boss" | "Raid Boss"
  questGiver?: boolean
}

export default function FarmingCalculatorPage() {
  const [playerLevel, setPlayerLevel] = useState(2550)
  const [playerStats, setPlayerStats] = useState({
    melee: 2550,
    sword: 2550,
    gun: 2550,
    fruit: 2550,
  })
  const [selectedSea, setSelectedSea] = useState<1 | 2 | 3>(3)
  const [bonuses, setBonuses] = useState({
    doubleXP: false,
    gamepass: false,
    code: false,
    premium: false,
  })
  const [autoCalculate, setAutoCalculate] = useState(true)
  const [showAdvanced, setShowAdvanced] = useState(false)

  const [results, setResults] = useState<any[]>([])
  const [isCalculating, setIsCalculating] = useState(false)

  // Données NPCs complètes et réalistes
  const npcsData: NPCData[] = [
    // Third Sea (2550+ content)
    {
      name: "Cake Prince",
      level: 2300,
      health: 2640000,
      location: "Cake Land",
      sea: 3,
      rewards: { xp: 62500, beli: 25000 },
      respawnTime: 8,
      difficulty: "Boss",
    },
    {
      name: "Dough King",
      level: 2300,
      health: 1111500,
      location: "Cake Land",
      sea: 3,
      rewards: { xp: 55000, beli: 32000 },
      respawnTime: 12,
      difficulty: "Raid Boss",
    },
    {
      name: "Cookie Crafter",
      level: 2200,
      health: 2640000,
      location: "Cake Land",
      sea: 3,
      rewards: { xp: 52500, beli: 24500 },
      respawnTime: 6,
      difficulty: "Hard",
    },
    {
      name: "Cake Guard",
      level: 2025,
      health: 2231250,
      location: "Cake Land",
      sea: 3,
      rewards: { xp: 47000, beli: 21000 },
      respawnTime: 5,
      difficulty: "Hard",
    },
    {
      name: "Baking Staff",
      level: 2100,
      health: 2415000,
      location: "Cake Land",
      sea: 3,
      rewards: { xp: 50000, beli: 23000 },
      respawnTime: 5,
      difficulty: "Hard",
    },
    {
      name: "Head Baker",
      level: 2275,
      health: 2640000,
      location: "Cake Land",
      sea: 3,
      rewards: { xp: 58000, beli: 27500 },
      respawnTime: 7,
      difficulty: "Hard",
    },
    {
      name: "Cocoa Warrior",
      level: 2175,
      health: 2570000,
      location: "Chocolate Land",
      sea: 3,
      rewards: { xp: 51500, beli: 24000 },
      respawnTime: 5,
      difficulty: "Hard",
    },
    {
      name: "Sweet Thief",
      level: 2125,
      health: 2460000,
      location: "Chocolate Land",
      sea: 3,
      rewards: { xp: 49500, beli: 23500 },
      respawnTime: 4,
      difficulty: "Medium",
    },
    {
      name: "Candy Rebel",
      level: 2050,
      health: 2290000,
      location: "Candy Land",
      sea: 3,
      rewards: { xp: 48000, beli: 22000 },
      respawnTime: 4,
      difficulty: "Medium",
    },
    {
      name: "Candy Pirate",
      level: 2000,
      health: 2200000,
      location: "Candy Land",
      sea: 3,
      rewards: { xp: 46500, beli: 21500 },
      respawnTime: 4,
      difficulty: "Medium",
    },
    {
      name: "Dragon Crew Warrior",
      level: 1575,
      health: 1312500,
      location: "Hydra Island",
      sea: 3,
      rewards: { xp: 7725, beli: 7700 },
      respawnTime: 4,
      difficulty: "Hard",
    },
    {
      name: "Dragon Crew Archer",
      level: 1600,
      health: 1360000,
      location: "Hydra Island",
      sea: 3,
      rewards: { xp: 8000, beli: 8000 },
      respawnTime: 4,
      difficulty: "Hard",
    },
    {
      name: "Pirate Millionaire",
      level: 1750,
      health: 4000000,
      location: "Haunted Castle",
      sea: 3,
      rewards: { xp: 425000, beli: 10000 },
      respawnTime: 10,
      difficulty: "Boss",
    },

    // Second Sea
    {
      name: "Marine Commodore",
      level: 900,
      health: 810000,
      location: "Marine Fortress",
      sea: 2,
      rewards: { xp: 4000, beli: 4000 },
      respawnTime: 6,
      difficulty: "Hard",
    },
    {
      name: "Female Islander",
      level: 525,
      health: 303750,
      location: "Port Town",
      sea: 2,
      rewards: { xp: 1775, beli: 1400 },
      respawnTime: 5,
      difficulty: "Medium",
    },
    {
      name: "Giant Islander",
      level: 650,
      health: 422500,
      location: "Port Town",
      sea: 2,
      rewards: { xp: 2350, beli: 1675 },
      respawnTime: 5,
      difficulty: "Medium",
    },
    {
      name: "Marine Captain",
      level: 350,
      health: 122500,
      location: "Marine Fortress",
      sea: 2,
      rewards: { xp: 1000, beli: 800 },
      respawnTime: 4,
      difficulty: "Medium",
    },
    {
      name: "Mercenary",
      level: 725,
      health: 551250,
      location: "Area 1",
      sea: 2,
      rewards: { xp: 2900, beli: 2400 },
      respawnTime: 5,
      difficulty: "Medium",
    },

    // First Sea
    {
      name: "Bandit",
      level: 5,
      health: 775,
      location: "Jungle",
      sea: 1,
      rewards: { xp: 18, beli: 25 },
      respawnTime: 3,
      difficulty: "Easy",
    },
    {
      name: "Monkey",
      level: 14,
      health: 1800,
      location: "Jungle",
      sea: 1,
      rewards: { xp: 35, beli: 60 },
      respawnTime: 3,
      difficulty: "Easy",
    },
    {
      name: "Pirate",
      level: 35,
      health: 2550,
      location: "Pirate Village",
      sea: 1,
      rewards: { xp: 100, beli: 300 },
      respawnTime: 4,
      difficulty: "Easy",
    },
    {
      name: "Marine",
      level: 60,
      health: 5200,
      location: "Marine Base",
      sea: 1,
      rewards: { xp: 180, beli: 420 },
      respawnTime: 4,
      difficulty: "Medium",
    },
  ]

  const calculateFarmingEfficiency = async () => {
    setIsCalculating(true)

    // Simuler un petit délai pour l'effet de calcul
    await new Promise((resolve) => setTimeout(resolve, 500))

    const filteredNPCs = npcsData.filter((npc) => npc.sea === selectedSea)

    const farmingResults = filteredNPCs.map((npc) => {
      // Calculer le temps de kill basé sur les stats du joueur
      const playerDamage = calculatePlayerDamage(npc)
      const timeToKill = Math.max(1, npc.health / playerDamage) // secondes

      // Calculer les bonus
      let xpMultiplier = 1
      if (bonuses.doubleXP) xpMultiplier *= 2
      if (bonuses.gamepass) xpMultiplier *= 1.5
      if (bonuses.code) xpMultiplier *= 1.25
      if (bonuses.premium) xpMultiplier *= 1.2

      const finalXP = npc.rewards.xp * xpMultiplier
      const finalBeli = npc.rewards.beli

      // Calculer l'efficacité par heure
      const cycleTime = timeToKill + npc.respawnTime
      const killsPerHour = 3600 / cycleTime
      const xpPerHour = finalXP * killsPerHour
      const beliPerHour = finalBeli * killsPerHour

      // Calculer le temps pour level up (approximatif)
      const xpNeededForNextLevel = getXPNeededForLevel(playerLevel + 1) - getXPNeededForLevel(playerLevel)
      const timeToLevelUp = xpNeededForNextLevel / (xpPerHour / 3600) // en secondes

      // Score d'efficacité basé sur XP/heure et niveau approprié
      const levelDifference = Math.abs(playerLevel - npc.level)
      const levelPenalty = levelDifference > 200 ? 0.5 : 1
      const efficiency = Math.round((xpPerHour / 1000) * levelPenalty)

      return {
        npc,
        timeToKill: Math.round(timeToKill),
        xpPerHour: Math.round(xpPerHour),
        beliPerHour: Math.round(beliPerHour),
        efficiency,
        timeToLevelUp: Math.round(timeToLevelUp / 60), // en minutes
        killsPerHour: Math.round(killsPerHour),
        levelDifference,
        isOptimal: levelDifference <= 100,
      }
    })

    // Trier par efficacité
    farmingResults.sort((a, b) => b.efficiency - a.efficiency)
    setResults(farmingResults)
    setIsCalculating(false)
  }

  const calculatePlayerDamage = (npc: NPCData) => {
    // Formule améliorée de dégâts basée sur le jeu
    const totalStats = playerStats.melee + playerStats.sword + playerStats.gun + playerStats.fruit
    const averageStats = totalStats / 4

    // Dégâts de base basés sur les stats
    const baseDamage = averageStats * 3.5 // Formule approximative du jeu

    // Bonus/malus de niveau
    const levelRatio = playerLevel / npc.level
    let levelMultiplier = 1

    if (levelRatio > 1.5)
      levelMultiplier = 1.8 // Très supérieur
    else if (levelRatio > 1.2)
      levelMultiplier = 1.4 // Supérieur
    else if (levelRatio > 0.8)
      levelMultiplier = 1.0 // Équilibré
    else if (levelRatio > 0.5)
      levelMultiplier = 0.7 // Inférieur
    else levelMultiplier = 0.4 // Très inférieur

    // Bonus de difficulté (plus difficile = plus de résistance)
    let difficultyMultiplier = 1
    switch (npc.difficulty) {
      case "Easy":
        difficultyMultiplier = 1.2
        break
      case "Medium":
        difficultyMultiplier = 1.0
        break
      case "Hard":
        difficultyMultiplier = 0.8
        break
      case "Boss":
        difficultyMultiplier = 0.6
        break
      case "Raid Boss":
        difficultyMultiplier = 0.4
        break
    }

    return Math.max(100, baseDamage * levelMultiplier * difficultyMultiplier)
  }

  const getXPNeededForLevel = (level: number) => {
    // Formule officielle approximative d'XP par niveau
    if (level <= 100) return level * 100
    if (level <= 300) return level * 150
    if (level <= 500) return level * 200
    if (level <= 700) return level * 300
    if (level <= 1000) return level * 500
    if (level <= 1500) return level * 1000
    if (level <= 2000) return level * 2000
    if (level <= 2550) return level * 5000
    return level * 10000
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Easy":
        return "bg-green-500/20 text-green-400 border-green-500/30"
      case "Medium":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
      case "Hard":
        return "bg-red-500/20 text-red-400 border-red-500/30"
      case "Boss":
        return "bg-purple-500/20 text-purple-400 border-purple-500/30"
      case "Raid Boss":
        return "bg-pink-500/20 text-pink-400 border-pink-500/30"
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30"
    }
  }

  const handleStatChange = (stat: keyof typeof playerStats, value: string) => {
    const numValue = Math.max(0, Math.min(2550, Number.parseInt(value) || 0))
    setPlayerStats({ ...playerStats, [stat]: numValue })
  }

  const handleLevelChange = (value: string) => {
    const numValue = Math.max(1, Math.min(2550, Number.parseInt(value) || 1))
    setPlayerLevel(numValue)
  }

  const resetToDefaults = () => {
    setPlayerLevel(2550)
    setPlayerStats({ melee: 2550, sword: 2550, gun: 2550, fruit: 2550 })
    setBonuses({ doubleXP: false, gamepass: false, code: false, premium: false })
    setSelectedSea(3)
  }

  const getTotalMultiplier = () => {
    let multiplier = 1
    if (bonuses.doubleXP) multiplier *= 2
    if (bonuses.gamepass) multiplier *= 1.5
    if (bonuses.code) multiplier *= 1.25
    if (bonuses.premium) multiplier *= 1.2
    return multiplier
  }

  useEffect(() => {
    if (autoCalculate) {
      calculateFarmingEfficiency()
    }
  }, [playerLevel, playerStats, selectedSea, bonuses, autoCalculate])

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="font-gaming text-3xl font-bold mb-2 flex items-center gap-2">
          <Clock className="w-8 h-8 text-primary" />
          Farming Calculator
        </h1>
        <p className="text-muted-foreground">Find the most efficient farming spots for your level and build</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <Card className="gaming-card">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-primary" />
              <div className="text-sm text-muted-foreground">Level</div>
            </div>
            <div className="font-mono text-xl font-bold">{playerLevel}</div>
          </CardContent>
        </Card>
        <Card className="gaming-card">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2">
              <Zap className="w-4 h-4 text-primary" />
              <div className="text-sm text-muted-foreground">Total Stats</div>
            </div>
            <div className="font-mono text-xl font-bold">
              {(playerStats.melee + playerStats.sword + playerStats.gun + playerStats.fruit).toLocaleString()}
            </div>
          </CardContent>
        </Card>
        <Card className="gaming-card">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2">
              <Award className="w-4 h-4 text-primary" />
              <div className="text-sm text-muted-foreground">XP Multiplier</div>
            </div>
            <div className="font-mono text-xl font-bold">{getTotalMultiplier().toFixed(1)}x</div>
          </CardContent>
        </Card>
        <Card className="gaming-card">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2">
              <MapPin className="w-4 h-4 text-primary" />
              <div className="text-sm text-muted-foreground">Current Sea</div>
            </div>
            <div className="font-mono text-xl font-bold">Sea {selectedSea}</div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Configuration Panel */}
        <Card className="gaming-card lg:col-span-1">
          <CardHeader>
            <CardTitle>Farming Setup</CardTitle>
            <CardDescription>Configure your character and bonuses</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="player-level">Player Level</Label>
              <Input
                id="player-level"
                type="number"
                value={playerLevel}
                onChange={(e) => handleLevelChange(e.target.value)}
                className="font-mono"
                min="1"
                max="2550"
              />
              {playerLevel < 100 && (
                <div className="flex items-center gap-1 mt-1 text-xs text-yellow-500">
                  <AlertTriangle className="w-3 h-3" />
                  Low level - consider quests first
                </div>
              )}
            </div>

            <div>
              <Label htmlFor="sea-select">Sea</Label>
              <Select
                value={selectedSea.toString()}
                onValueChange={(value) => setSelectedSea(Number.parseInt(value) as 1 | 2 | 3)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">First Sea (Lv 1-700)</SelectItem>
                  <SelectItem value="2">Second Sea (Lv 700-1500)</SelectItem>
                  <SelectItem value="3">Third Sea (Lv 1500+)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <Label>Stats Distribution</Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="melee" className="text-xs">
                    Melee
                  </Label>
                  <Input
                    id="melee"
                    type="number"
                    value={playerStats.melee}
                    onChange={(e) => handleStatChange("melee", e.target.value)}
                    className="font-mono text-xs"
                    min="0"
                    max="2550"
                  />
                </div>
                <div>
                  <Label htmlFor="sword" className="text-xs">
                    Sword
                  </Label>
                  <Input
                    id="sword"
                    type="number"
                    value={playerStats.sword}
                    onChange={(e) => handleStatChange("sword", e.target.value)}
                    className="font-mono text-xs"
                    min="0"
                    max="2550"
                  />
                </div>
                <div>
                  <Label htmlFor="gun" className="text-xs">
                    Gun
                  </Label>
                  <Input
                    id="gun"
                    type="number"
                    value={playerStats.gun}
                    onChange={(e) => handleStatChange("gun", e.target.value)}
                    className="font-mono text-xs"
                    min="0"
                    max="2550"
                  />
                </div>
                <div>
                  <Label htmlFor="fruit" className="text-xs">
                    Fruit
                  </Label>
                  <Input
                    id="fruit"
                    type="number"
                    value={playerStats.fruit}
                    onChange={(e) => handleStatChange("fruit", e.target.value)}
                    className="font-mono text-xs"
                    min="0"
                    max="2550"
                  />
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <Label>Active Bonuses</Label>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="double-xp" className="text-sm">
                    2x XP Event
                  </Label>
                  <Switch
                    id="double-xp"
                    checked={bonuses.doubleXP}
                    onCheckedChange={(checked: boolean) => setBonuses({ ...bonuses, doubleXP: checked })}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="gamepass" className="text-sm">
                    2x XP Gamepass
                  </Label>
                  <Switch
                    id="gamepass"
                    checked={bonuses.gamepass}
                    onCheckedChange={(checked: boolean) => setBonuses({ ...bonuses, gamepass: checked })}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="code" className="text-sm">
                    XP Code (+25%)
                  </Label>
                  <Switch
                    id="code"
                    checked={bonuses.code}
                    onCheckedChange={(checked: boolean) => setBonuses({ ...bonuses, code: checked })}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="premium" className="text-sm">
                    Premium (+20%)
                  </Label>
                  <Switch
                    id="premium"
                    checked={bonuses.premium}
                    onCheckedChange={(checked: boolean) => setBonuses({ ...bonuses, premium: checked })}
                  />
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="auto-calc" className="text-sm">
                  Auto Calculate
                </Label>
                <Switch id="auto-calc" checked={autoCalculate} onCheckedChange={setAutoCalculate} />
              </div>
            </div>

            <div className="flex gap-2">
              <Button onClick={calculateFarmingEfficiency} className="flex-1 glow-effect" disabled={isCalculating}>
                <TrendingUp className="w-4 h-4 mr-2" />
                {isCalculating ? "Calculating..." : "Calculate"}
              </Button>
              <Button variant="outline" onClick={resetToDefaults}>
                Reset
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Results Panel */}
        <div className="lg:col-span-3 space-y-4">
          <Card className="gaming-card">
            <CardHeader>
              <CardTitle>Farming Recommendations</CardTitle>
              <CardDescription>
                Ranked by XP efficiency for your current setup • Showing {results.length} NPCs in Sea {selectedSea}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isCalculating ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Calculating optimal farming spots...</p>
                </div>
              ) : results.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Clock className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>Configure your setup and click "Calculate" to see recommendations</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {results.slice(0, 15).map((result, index) => (
                    <Card
                      key={result.npc.name}
                      className={`bg-secondary/20 border-border/50 ${
                        result.isOptimal ? "ring-1 ring-green-500/30" : ""
                      } ${result.levelDifference > 300 ? "opacity-60" : ""}`}
                    >
                      <CardContent className="pt-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <div
                              className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm ${
                                index === 0
                                  ? "bg-yellow-500/20 text-yellow-400"
                                  : index === 1
                                    ? "bg-gray-400/20 text-gray-300"
                                    : index === 2
                                      ? "bg-orange-500/20 text-orange-400"
                                      : "bg-primary/20 text-primary"
                              }`}
                            >
                              {index + 1}
                            </div>
                            <div>
                              <div className="flex items-center gap-2">
                                <h3 className="font-semibold">{result.npc.name}</h3>
                                {result.isOptimal && (
                                  <Badge
                                    variant="outline"
                                    className="bg-green-500/20 text-green-400 border-green-500/30 text-xs"
                                  >
                                    Optimal
                                  </Badge>
                                )}
                              </div>
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <MapPin className="w-3 h-3" />
                                {result.npc.location}
                                <Badge variant="outline" className={getDifficultyColor(result.npc.difficulty)}>
                                  {result.npc.difficulty}
                                </Badge>
                                <span className="text-xs">Lv {result.npc.level}</span>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-mono text-lg font-bold text-primary">{result.efficiency}k</div>
                            <div className="text-xs text-muted-foreground">Efficiency Score</div>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                          <div>
                            <div className="text-muted-foreground">XP/Hour</div>
                            <div className="font-mono font-semibold">{result.xpPerHour.toLocaleString()}</div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Beli/Hour</div>
                            <div className="font-mono font-semibold">{result.beliPerHour.toLocaleString()}</div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Kill Time</div>
                            <div className="font-mono font-semibold">{result.timeToKill}s</div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Kills/Hour</div>
                            <div className="font-mono font-semibold">{result.killsPerHour}</div>
                          </div>
                          <div>
                            <div className="text-muted-foreground">Level Up</div>
                            <div className="font-mono font-semibold">
                              {result.timeToLevelUp > 60
                                ? `${Math.round(result.timeToLevelUp / 60)}h`
                                : `${result.timeToLevelUp}m`}
                            </div>
                          </div>
                        </div>

                        {result.levelDifference > 200 && (
                          <div className="mt-3 pt-3 border-t border-border/50">
                            <div className="flex items-center gap-1 text-xs text-yellow-500">
                              <AlertTriangle className="w-3 h-3" />
                              Level difference too high - reduced efficiency
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
