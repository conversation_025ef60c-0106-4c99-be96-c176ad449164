import { BaseScraper } from "../base-scraper"
import { ScrapedItem, EnemyData } from "../types"

export class EnemyScraper extends BaseScraper {
  
  extractSpecificData(wikitext: string, infoboxData: Record<string, string>): EnemyData {
    const enemyData: any = {}

    // Extract enemy type
    if (infoboxData.type) {
      const type = infoboxData.type.trim()
      if (type === "Enemy") {
        // Determine subtype based on content
        if (wikitext.toLowerCase().includes("raid")) {
          enemyData.enemyType = "Raid"
        } else if (wikitext.toLowerCase().includes("boss")) {
          enemyData.enemyType = "Boss"
        } else if (wikitext.toLowerCase().includes("elite")) {
          enemyData.enemyType = "Elite"
        } else {
          enemyData.enemyType = "Regular"
        }
      }
    }

    // Extract base stats
    if (infoboxData.hp) {
      const hp = this.parseNumber(infoboxData.hp)
      if (hp) enemyData.hp = hp
    }

    if (infoboxData.level) {
      const level = this.parseNumber(infoboxData.level)
      if (level) enemyData.level = level
    }

    if (infoboxData.baseatk || infoboxData.baseattack) {
      const baseAttack = this.parseNumber(infoboxData.baseatk || infoboxData.baseattack)
      if (baseAttack) enemyData.baseAttack = baseAttack
    }

    // Extract spawn locations
    const spawnLocations: string[] = []
    if (infoboxData.location) spawnLocations.push(infoboxData.location)
    if (infoboxData.location1) spawnLocations.push(infoboxData.location1)
    if (infoboxData.location2) spawnLocations.push(infoboxData.location2)
    if (infoboxData.location3) spawnLocations.push(infoboxData.location3)

    if (spawnLocations.length > 0) {
      enemyData.spawnLocation = spawnLocations
    }

    // Extract weapon used
    if (infoboxData.weapon && infoboxData.weapon !== "None") {
      enemyData.weapon = infoboxData.weapon
    }

    // Extract aura
    if (infoboxData.aura) {
      enemyData.aura = infoboxData.aura.toLowerCase() === "yes"
    }

    // Extract immunities
    const immunities: string[] = []
    if (infoboxData.immunity && infoboxData.immunity !== "N/A") {
      immunities.push(infoboxData.immunity)
    }

    // Look for additional immunities in text
    const immunityPatterns = [
      /immune to ([^.]+)/gi,
      /immunity to ([^.]+)/gi,
      /cannot be affected by ([^.]+)/gi
    ]

    immunityPatterns.forEach(pattern => {
      let match
      while ((match = pattern.exec(wikitext)) !== null) {
        const immunity = this.cleanWikitext(match[1])
        if (immunity && immunity.length > 2 && !immunities.includes(immunity)) {
          immunities.push(immunity)
        }
      }
    })

    if (immunities.length > 0) {
      enemyData.immunity = [...new Set(immunities)]
    }

    // Extract attacks from behavior tables
    const behaviorMatch = wikitext.match(/==\s*Behavior\s*==([\s\S]*?)(?===|$)/i)
    if (behaviorMatch) {
      const attacks: Array<{ name: string; description: string; howToAvoid: string }> = []
      const behaviorContent = behaviorMatch[1]

      // Look for attack tables
      const tableMatch = behaviorContent.match(/\{\|\s*class="fandom-table"[\s\S]*?\|\}/)
      if (tableMatch) {
        const tableContent = tableMatch[0]
        const rows = tableContent.split('|-').slice(1) // Skip header

        rows.forEach(row => {
          const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell && !cell.startsWith('{') && !cell.startsWith('!'))

          if (cells.length >= 3) {
            const attackName = this.cleanWikitext(cells[0])
            const description = this.cleanWikitext(cells[1])
            const howToAvoid = this.cleanWikitext(cells[2])

            if (attackName && description && howToAvoid) {
              attacks.push({
                name: attackName,
                description: description,
                howToAvoid: howToAvoid
              })
            }
          }
        })
      }

      if (attacks.length > 0) {
        enemyData.attacks = attacks
      }

      // Extract general behavior
      const behaviorLines = behaviorContent.split('\n')
        .filter(line => !line.includes('|') && !line.includes('{') && line.trim().length > 20)
        .map(line => this.cleanWikitext(line.trim()))
        .filter(line => line.length > 10)

      if (behaviorLines.length > 0) {
        enemyData.behavior = behaviorLines[0]
      }
    }

    // Extract attacks from moveset sections
    const movesetMatch = wikitext.match(/==\s*Moveset\s*==([\s\S]*?)(?===|$)/i)
    if (movesetMatch && !enemyData.attacks) {
      const attacks: Array<{ name: string; description: string; howToAvoid: string }> = []
      const movesetContent = movesetMatch[1]

      // Look for move descriptions
      const moveLines = movesetContent.split('\n').filter(line => line.trim().startsWith('*'))

      moveLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 20) {
          // Try to extract move name and description
          const moveMatch = cleanLine.match(/^([^:]+):\s*(.+)/)
          if (moveMatch) {
            attacks.push({
              name: moveMatch[1].trim(),
              description: moveMatch[2].trim(),
              howToAvoid: "Dodge or use appropriate counter"
            })
          } else {
            attacks.push({
              name: "Unknown Attack",
              description: cleanLine,
              howToAvoid: "Dodge or use appropriate counter"
            })
          }
        }
      })

      if (attacks.length > 0) {
        enemyData.attacks = attacks
      }
    }

    // Extract drops
    const drops: string[] = []
    if (infoboxData.drop) drops.push(infoboxData.drop)
    if (infoboxData.drop1) drops.push(infoboxData.drop1)
    if (infoboxData.drop2) drops.push(infoboxData.drop2)
    if (infoboxData.drop3) drops.push(infoboxData.drop3)

    // Look for drops in text
    const dropsMatch = wikitext.match(/==\s*Drops?\s*==([\s\S]*?)(?===|$)/i)
    if (dropsMatch) {
      const dropLines = dropsMatch[1].split('\n').filter(line => line.trim().startsWith('*'))
      dropLines.forEach(line => {
        const cleanDrop = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanDrop.length > 3 && !drops.includes(cleanDrop)) {
          drops.push(cleanDrop)
        }
      })
    }

    if (drops.length > 0) {
      enemyData.drops = [...new Set(drops)]
    }

    // Extract spawn time
    if (infoboxData.spawn) {
      enemyData.spawnTime = infoboxData.spawn
    }

    // Extract additional stats from text
    const statsPatterns = [
      { pattern: /health:\s*([0-9,]+)/i, key: 'hp' },
      { pattern: /hp:\s*([0-9,]+)/i, key: 'hp' },
      { pattern: /level:\s*(\d+)/i, key: 'level' },
      { pattern: /damage:\s*([0-9,]+)/i, key: 'baseAttack' }
    ]

    statsPatterns.forEach(({ pattern, key }) => {
      if (!enemyData[key]) {
        const match = wikitext.match(pattern)
        if (match) {
          const value = this.parseNumber(match[1])
          if (value) enemyData[key] = value
        }
      }
    })

    return enemyData
  }

  async scrapeItem(title: string, itemType: string): Promise<ScrapedItem | null> {
    console.log(`👹 Scraping enemy: ${title}`)

    const wikitext = await this.getPageContent(title)
    if (!wikitext) {
      console.warn(`⚠️ No content found for ${title}`)
      return null
    }

    // Extract infobox data
    const infoboxData = this.extractTemplateData(wikitext, "Infobox")

    // Extract enemy-specific data
    const enemyData = this.extractSpecificData(wikitext, infoboxData)

    // Create the scraped item
    const item: ScrapedItem = {
      name: title,
      type: itemType,
      category: enemyData.enemyType?.toLowerCase() || "enemy",
      wikiUrl: `https://blox-fruits.fandom.com/wiki/${encodeURIComponent(title)}`,
      lastUpdated: new Date(),
      enemyData,
      rawData: {
        infobox: infoboxData,
        wikitextLength: wikitext.length,
        movesFound: enemyData.attacks?.length || 0,
        statsFound: 0,
        extractedAt: new Date().toISOString(),
        enemyData
      }
    }

    // Extract basic properties from infobox
    if (infoboxData.description) item.description = this.cleanWikitext(infoboxData.description)
    if (enemyData.spawnLocation && enemyData.spawnLocation.length > 0) {
      item.location = enemyData.spawnLocation[0]
    }
    if (enemyData.level) item.level = enemyData.level
    if (enemyData.hp) item.hp = enemyData.hp

    return item
  }

  async scrapeCategory(categoryName: string = "Raids"): Promise<ScrapedItem[]> {
    console.log(`\n🎯 Starting to scrape enemies from category: ${categoryName}`)

    const members = await this.getCategoryMembers(categoryName)
    if (members.length === 0) {
      return []
    }

    const items: ScrapedItem[] = []
    let processed = 0

    for (const member of members) {
      try {
        const item = await this.scrapeItem(member.title, "enemy")
        if (item) {
          items.push(item)
        }
        processed++

        // Progress indicator
        if (processed % 5 === 0 || processed === members.length) {
          console.log(
            `📊 Progress: ${processed}/${members.length} (${Math.round((processed / members.length) * 100)}%)`
          )
        }

        // Small delay between items
        await new Promise((resolve) => setTimeout(resolve, 200))
      } catch (error) {
        console.error(`❌ Error scraping enemy ${member.title}:`, error)
      }
    }

    console.log(`✅ Completed enemies: ${items.length}/${members.length} items scraped`)
    return items
  }
}
